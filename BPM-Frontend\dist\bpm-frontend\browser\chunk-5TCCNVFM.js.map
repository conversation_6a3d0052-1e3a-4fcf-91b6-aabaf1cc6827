{"version": 3, "sources": ["src/app/features/admin/components/system-settings/system-settings.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\n\n@Component({\n  selector: 'app-system-settings',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatSlideToggleModule,\n    MatDividerModule,\n    MatSnackBarModule,\n    MatTabsModule\n  ],\n  template: `\n    <div class=\"system-settings-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>settings</mat-icon>\n            System Settings\n          </mat-card-title>\n        </mat-card-header>\n\n        <mat-card-content>\n          <mat-tab-group>\n            <!-- General Settings -->\n            <mat-tab label=\"General\">\n              <div class=\"tab-content\">\n                <form [formGroup]=\"generalForm\" (ngSubmit)=\"saveGeneralSettings()\">\n                  <div class=\"settings-section\">\n                    <h3>Application Settings</h3>\n                    \n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\" class=\"full-width\">\n                        <mat-label>Application Name</mat-label>\n                        <input matInput formControlName=\"applicationName\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\" class=\"full-width\">\n                        <mat-label>Company Name</mat-label>\n                        <input matInput formControlName=\"companyName\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Default Language</mat-label>\n                        <mat-select formControlName=\"defaultLanguage\">\n                          <mat-option value=\"en\">English</mat-option>\n                          <mat-option value=\"fr\">French</mat-option>\n                          <mat-option value=\"es\">Spanish</mat-option>\n                        </mat-select>\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Timezone</mat-label>\n                        <mat-select formControlName=\"timezone\">\n                          <mat-option value=\"UTC\">UTC</mat-option>\n                          <mat-option value=\"America/New_York\">Eastern Time</mat-option>\n                          <mat-option value=\"America/Los_Angeles\">Pacific Time</mat-option>\n                          <mat-option value=\"Europe/London\">London</mat-option>\n                        </mat-select>\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"maintenanceMode\">\n                        Maintenance Mode\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Enable maintenance mode to prevent user access during updates</p>\n                    </div>\n                  </div>\n\n                  <mat-divider></mat-divider>\n\n                  <div class=\"settings-section\">\n                    <h3>Security Settings</h3>\n                    \n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Session Timeout (minutes)</mat-label>\n                        <input matInput type=\"number\" formControlName=\"sessionTimeout\" min=\"5\" max=\"480\">\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>Password Min Length</mat-label>\n                        <input matInput type=\"number\" formControlName=\"passwordMinLength\" min=\"6\" max=\"20\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"requirePasswordComplexity\">\n                        Require Password Complexity\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Require uppercase, lowercase, numbers, and special characters</p>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"enableTwoFactor\">\n                        Enable Two-Factor Authentication\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Require 2FA for all admin users</p>\n                    </div>\n                  </div>\n\n                  <div class=\"actions\">\n                    <button type=\"submit\" mat-raised-button color=\"primary\">\n                      <mat-icon>save</mat-icon>\n                      Save General Settings\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </mat-tab>\n\n            <!-- Notification Settings -->\n            <mat-tab label=\"Notifications\">\n              <div class=\"tab-content\">\n                <form [formGroup]=\"notificationForm\" (ngSubmit)=\"saveNotificationSettings()\">\n                  <div class=\"settings-section\">\n                    <h3>Email Notifications</h3>\n                    \n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\" class=\"full-width\">\n                        <mat-label>SMTP Server</mat-label>\n                        <input matInput formControlName=\"smtpServer\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"form-row\">\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>SMTP Port</mat-label>\n                        <input matInput type=\"number\" formControlName=\"smtpPort\">\n                      </mat-form-field>\n\n                      <mat-form-field appearance=\"outline\">\n                        <mat-label>From Email</mat-label>\n                        <input matInput type=\"email\" formControlName=\"fromEmail\">\n                      </mat-form-field>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"enableEmailNotifications\">\n                        Enable Email Notifications\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Send email notifications for workflow events</p>\n                    </div>\n                  </div>\n\n                  <mat-divider></mat-divider>\n\n                  <div class=\"settings-section\">\n                    <h3>System Notifications</h3>\n                    \n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"notifyOnRequestSubmission\">\n                        Request Submission\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Notify managers when new requests are submitted</p>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"notifyOnRequestApproval\">\n                        Request Approval\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Notify requesters when their requests are approved/rejected</p>\n                    </div>\n\n                    <div class=\"toggle-section\">\n                      <mat-slide-toggle formControlName=\"notifyOnOverdueRequests\">\n                        Overdue Requests\n                      </mat-slide-toggle>\n                      <p class=\"toggle-description\">Send reminders for overdue approval requests</p>\n                    </div>\n                  </div>\n\n                  <div class=\"actions\">\n                    <button type=\"submit\" mat-raised-button color=\"primary\">\n                      <mat-icon>save</mat-icon>\n                      Save Notification Settings\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </mat-tab>\n\n            <!-- Backup & Maintenance -->\n            <mat-tab label=\"Backup\">\n              <div class=\"tab-content\">\n                <div class=\"settings-section\">\n                  <h3>Database Backup</h3>\n                  \n                  <div class=\"backup-info\">\n                    <p><strong>Last Backup:</strong> {{lastBackupDate | date:'full'}}</p>\n                    <p><strong>Backup Size:</strong> {{lastBackupSize}}</p>\n                    <p><strong>Status:</strong> <span class=\"backup-status success\">Healthy</span></p>\n                  </div>\n\n                  <div class=\"backup-actions\">\n                    <button mat-raised-button color=\"primary\" (click)=\"createBackup()\">\n                      <mat-icon>backup</mat-icon>\n                      Create Backup Now\n                    </button>\n                    <button mat-button (click)=\"downloadBackup()\">\n                      <mat-icon>download</mat-icon>\n                      Download Latest Backup\n                    </button>\n                  </div>\n                </div>\n\n                <mat-divider></mat-divider>\n\n                <div class=\"settings-section\">\n                  <h3>System Maintenance</h3>\n                  \n                  <div class=\"maintenance-actions\">\n                    <button mat-raised-button (click)=\"clearCache()\">\n                      <mat-icon>clear_all</mat-icon>\n                      Clear Cache\n                    </button>\n                    <button mat-raised-button (click)=\"optimizeDatabase()\">\n                      <mat-icon>tune</mat-icon>\n                      Optimize Database\n                    </button>\n                    <button mat-raised-button color=\"warn\" (click)=\"restartSystem()\">\n                      <mat-icon>restart_alt</mat-icon>\n                      Restart System\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </mat-tab>\n          </mat-tab-group>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .system-settings-container {\n      padding: 1rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .tab-content {\n      padding: 1rem 0;\n    }\n\n    .settings-section {\n      margin-bottom: 2rem;\n    }\n\n    .settings-section h3 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .toggle-section {\n      margin-bottom: 1rem;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .toggle-description {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n      font-size: 0.9rem;\n    }\n\n    .actions {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .backup-info {\n      background-color: #f8f9fa;\n      padding: 1rem;\n      border-radius: 8px;\n      margin-bottom: 1rem;\n    }\n\n    .backup-info p {\n      margin: 0.5rem 0;\n    }\n\n    .backup-status {\n      padding: 2px 8px;\n      border-radius: 4px;\n      font-size: 0.8rem;\n      font-weight: bold;\n    }\n\n    .backup-status.success {\n      background-color: #d4edda;\n      color: #155724;\n    }\n\n    .backup-actions,\n    .maintenance-actions {\n      display: flex;\n      gap: 1rem;\n      flex-wrap: wrap;\n    }\n\n    @media (max-width: 768px) {\n      .form-row {\n        flex-direction: column;\n      }\n\n      .backup-actions,\n      .maintenance-actions {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class SystemSettingsComponent implements OnInit {\n  generalForm!: FormGroup;\n  notificationForm!: FormGroup;\n  \n  lastBackupDate = new Date();\n  lastBackupSize = '2.3 GB';\n\n  constructor(\n    private readonly fb: FormBuilder,\n    private readonly snackBar: MatSnackBar\n  ) {\n    this.initForms();\n  }\n\n  ngOnInit(): void {\n    this.loadSettings();\n  }\n\n  initForms(): void {\n    this.generalForm = this.fb.group({\n      applicationName: ['BPM Light', Validators.required],\n      companyName: ['Your Company', Validators.required],\n      defaultLanguage: ['en'],\n      timezone: ['UTC'],\n      maintenanceMode: [false],\n      sessionTimeout: [60, [Validators.min(5), Validators.max(480)]],\n      passwordMinLength: [8, [Validators.min(6), Validators.max(20)]],\n      requirePasswordComplexity: [true],\n      enableTwoFactor: [false]\n    });\n\n    this.notificationForm = this.fb.group({\n      smtpServer: ['smtp.company.com'],\n      smtpPort: [587],\n      fromEmail: ['<EMAIL>', Validators.email],\n      enableEmailNotifications: [true],\n      notifyOnRequestSubmission: [true],\n      notifyOnRequestApproval: [true],\n      notifyOnOverdueRequests: [true]\n    });\n  }\n\n  loadSettings(): void {\n    // In a real application, load settings from the backend\n    console.log('Loading system settings...');\n  }\n\n  saveGeneralSettings(): void {\n    if (this.generalForm.valid) {\n      this.snackBar.open('General settings saved successfully', 'Close', { duration: 3000 });\n    }\n  }\n\n  saveNotificationSettings(): void {\n    if (this.notificationForm.valid) {\n      this.snackBar.open('Notification settings saved successfully', 'Close', { duration: 3000 });\n    }\n  }\n\n  createBackup(): void {\n    this.snackBar.open('Creating backup...', 'Close', { duration: 3000 });\n  }\n\n  downloadBackup(): void {\n    this.snackBar.open('Downloading backup...', 'Close', { duration: 3000 });\n  }\n\n  clearCache(): void {\n    this.snackBar.open('Cache cleared successfully', 'Close', { duration: 3000 });\n  }\n\n  optimizeDatabase(): void {\n    this.snackBar.open('Database optimization started', 'Close', { duration: 3000 });\n  }\n\n  restartSystem(): void {\n    this.snackBar.open('System restart initiated', 'Close', { duration: 3000 });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmWM,IAAO,0BAAP,MAAO,yBAAuB;EAQf;EACA;EARnB;EACA;EAEA,iBAAiB,oBAAI,KAAI;EACzB,iBAAiB;EAEjB,YACmB,IACA,UAAqB;AADrB,SAAA,KAAA;AACA,SAAA,WAAA;AAEjB,SAAK,UAAS;EAChB;EAEA,WAAQ;AACN,SAAK,aAAY;EACnB;EAEA,YAAS;AACP,SAAK,cAAc,KAAK,GAAG,MAAM;MAC/B,iBAAiB,CAAC,aAAa,WAAW,QAAQ;MAClD,aAAa,CAAC,gBAAgB,WAAW,QAAQ;MACjD,iBAAiB,CAAC,IAAI;MACtB,UAAU,CAAC,KAAK;MAChB,iBAAiB,CAAC,KAAK;MACvB,gBAAgB,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,GAAG,CAAC,CAAC;MAC7D,mBAAmB,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC;MAC9D,2BAA2B,CAAC,IAAI;MAChC,iBAAiB,CAAC,KAAK;KACxB;AAED,SAAK,mBAAmB,KAAK,GAAG,MAAM;MACpC,YAAY,CAAC,kBAAkB;MAC/B,UAAU,CAAC,GAAG;MACd,WAAW,CAAC,uBAAuB,WAAW,KAAK;MACnD,0BAA0B,CAAC,IAAI;MAC/B,2BAA2B,CAAC,IAAI;MAChC,yBAAyB,CAAC,IAAI;MAC9B,yBAAyB,CAAC,IAAI;KAC/B;EACH;EAEA,eAAY;AAEV,YAAQ,IAAI,4BAA4B;EAC1C;EAEA,sBAAmB;AACjB,QAAI,KAAK,YAAY,OAAO;AAC1B,WAAK,SAAS,KAAK,uCAAuC,SAAS,EAAE,UAAU,IAAI,CAAE;IACvF;EACF;EAEA,2BAAwB;AACtB,QAAI,KAAK,iBAAiB,OAAO;AAC/B,WAAK,SAAS,KAAK,4CAA4C,SAAS,EAAE,UAAU,IAAI,CAAE;IAC5F;EACF;EAEA,eAAY;AACV,SAAK,SAAS,KAAK,sBAAsB,SAAS,EAAE,UAAU,IAAI,CAAE;EACtE;EAEA,iBAAc;AACZ,SAAK,SAAS,KAAK,yBAAyB,SAAS,EAAE,UAAU,IAAI,CAAE;EACzE;EAEA,aAAU;AACR,SAAK,SAAS,KAAK,8BAA8B,SAAS,EAAE,UAAU,IAAI,CAAE;EAC9E;EAEA,mBAAgB;AACd,SAAK,SAAS,KAAK,iCAAiC,SAAS,EAAE,UAAU,IAAI,CAAE;EACjF;EAEA,gBAAa;AACX,SAAK,SAAS,KAAK,4BAA4B,SAAS,EAAE,UAAU,IAAI,CAAE;EAC5E;;qCA7EW,0BAAuB,4BAAA,WAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,KAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,2BAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,cAAA,WAAA,GAAA,YAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,iBAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,aAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,kBAAA,OAAA,KAAA,OAAA,KAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,qBAAA,OAAA,KAAA,OAAA,IAAA,GAAA,CAAA,mBAAA,2BAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,QAAA,UAAA,qBAAA,IAAA,SAAA,SAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,YAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,UAAA,GAAA,CAAA,YAAA,IAAA,QAAA,SAAA,mBAAA,WAAA,GAAA,CAAA,mBAAA,0BAAA,GAAA,CAAA,mBAAA,2BAAA,GAAA,CAAA,mBAAA,yBAAA,GAAA,CAAA,mBAAA,yBAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,cAAA,IAAA,GAAA,OAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,qBAAA,IAAA,GAAA,OAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,QAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAlUhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuC,GAAA,UAAA,EAC3B,GAAA,iBAAA,EACS,GAAA,gBAAA,EACC,GAAA,UAAA;AACJ,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,iBAAA,GAAA,mBAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,eAAA,EACD,GAAA,WAAA,CAAA,EAEY,IAAA,OAAA,CAAA,EACE,IAAA,QAAA,CAAA;AACS,MAAA,qBAAA,YAAA,SAAA,6DAAA;AAAA,eAAY,IAAA,oBAAA;MAAqB,CAAA;AAC/D,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,IAAA;AACxB,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AAExB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACoC,IAAA,WAAA;AAC3C,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC3B,MAAA,oBAAA,IAAA,SAAA,CAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACoC,IAAA,WAAA;AAC3C,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACvB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACiB,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,cAAA,EAAA,EAA8C,IAAA,cAAA,EAAA;AACrB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAa,EAChC;AAGf,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACnB,MAAA,yBAAA,IAAA,cAAA,EAAA,EAAuC,IAAA,cAAA,EAAA;AACb,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAqC,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACpD,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAkC,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAa,EAC1C,EACE;AAGnB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,IAAA,oBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,+DAAA;AAA6D,MAAA,uBAAA,EAAI,EAC3F;AAGR,MAAA,oBAAA,IAAA,aAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,IAAA;AACxB,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AAErB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACiB,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AACpC,MAAA,oBAAA,IAAA,SAAA,EAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC9B,MAAA,oBAAA,IAAA,SAAA,EAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,IAAA,+BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,+DAAA;AAA6D,MAAA,uBAAA,EAAI;AAGjG,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,IAAA,oCAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,iCAAA;AAA+B,MAAA,uBAAA,EAAI,EAC7D;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,UAAA,EAAA,EACqC,IAAA,UAAA;AAC5C,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,yBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD,EACH;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAA+B,IAAA,OAAA,CAAA,EACJ,IAAA,QAAA,CAAA;AACc,MAAA,qBAAA,YAAA,SAAA,6DAAA;AAAA,eAAY,IAAA,yBAAA;MAA0B,CAAA;AACzE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,IAAA;AACxB,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AAEvB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACoC,IAAA,WAAA;AAC3C,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACtB,MAAA,oBAAA,IAAA,SAAA,EAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,kBAAA,CAAA,EACiB,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACpB,MAAA,oBAAA,IAAA,SAAA,EAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrB,MAAA,oBAAA,KAAA,SAAA,EAAA;AACF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,KAAA,8BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,KAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAI,EAC1E;AAGR,MAAA,oBAAA,KAAA,aAAA;AAEA,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA8B,KAAA,IAAA;AACxB,MAAA,iBAAA,KAAA,sBAAA;AAAoB,MAAA,uBAAA;AAExB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,KAAA,sBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,KAAA,iDAAA;AAA+C,MAAA,uBAAA,EAAI;AAGnF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,KAAA,oBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,KAAA,6DAAA;AAA2D,MAAA,uBAAA,EAAI;AAG/F,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,oBAAA,EAAA;AAExB,MAAA,iBAAA,KAAA,oBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,KAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAI,EAC1E;AAGR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAqB,KAAA,UAAA,EAAA,EACqC,KAAA,UAAA;AAC5C,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,KAAA,8BAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD,EACH;AAIR,MAAA,yBAAA,KAAA,WAAA,EAAA,EAAwB,KAAA,OAAA,CAAA,EACG,KAAA,OAAA,CAAA,EACO,KAAA,IAAA;AACxB,MAAA,iBAAA,KAAA,iBAAA;AAAe,MAAA,uBAAA;AAEnB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAyB,KAAA,GAAA,EACpB,KAAA,QAAA;AAAQ,MAAA,iBAAA,KAAA,cAAA;AAAY,MAAA,uBAAA;AAAU,MAAA,iBAAA,GAAA;;AAAgC,MAAA,uBAAA;AACjE,MAAA,yBAAA,KAAA,GAAA,EAAG,KAAA,QAAA;AAAQ,MAAA,iBAAA,KAAA,cAAA;AAAY,MAAA,uBAAA;AAAU,MAAA,iBAAA,GAAA;AAAkB,MAAA,uBAAA;AACnD,MAAA,yBAAA,KAAA,GAAA,EAAG,KAAA,QAAA;AAAQ,MAAA,iBAAA,KAAA,SAAA;AAAO,MAAA,uBAAA;AAAU,MAAA,yBAAA,KAAA,QAAA,EAAA;AAAoC,MAAA,iBAAA,KAAA,SAAA;AAAO,MAAA,uBAAA,EAAO,EAAI;AAGpF,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,UAAA,EAAA;AACgB,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,aAAA;MAAc,CAAA;AAC/D,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,iBAAA,KAAA,qBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,UAAA,EAAA;AAAmB,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,eAAA;MAAgB,CAAA;AAC1C,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,iBAAA,KAAA,0BAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,oBAAA,KAAA,aAAA;AAEA,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA8B,KAAA,IAAA;AACxB,MAAA,iBAAA,KAAA,oBAAA;AAAkB,MAAA,uBAAA;AAEtB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAiC,KAAA,UAAA,EAAA;AACL,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,WAAA;MAAY,CAAA;AAC7C,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,WAAA;AAAS,MAAA,uBAAA;AACnB,MAAA,iBAAA,KAAA,eAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,UAAA,EAAA;AAA0B,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,iBAAA;MAAkB,CAAA;AACnD,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,KAAA,qBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,UAAA,EAAA;AAAuC,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,cAAA;MAAe,CAAA;AAC7D,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,aAAA;AAAW,MAAA,uBAAA;AACrB,MAAA,iBAAA,KAAA,kBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF,EACF,EACE,EACI,EACC,EACV;;;AAhNK,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,WAAA;AA4FA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,gBAAA;AA2E+B,MAAA,oBAAA,EAAA;AAAA,MAAA,6BAAA,KAAA,sBAAA,KAAA,GAAA,IAAA,gBAAA,MAAA,GAAA,EAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,gBAAA,EAAA;;;IArMjD;IAAY;IACZ;IAAW;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACX;IAAmB;IAAA;IACnB;IAAa;IAAA;IAAA;IAAA;IACb;IAAe;IACf;IAAa;IACb;IAAkB;IAAA;IAClB;IAAc;IACd;IAAe;IAAA;IACf;IAAoB;IACpB;IAAgB;IAChB;IACA;IAAa;IAAA;EAAA,GAAA,QAAA,CAAA,+vDAAA,EAAA,CAAA;;;sEAqUJ,yBAAuB,CAAA;UArVnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiOT,QAAA,CAAA,2kDAAA,EAAA,CAAA;;;;6EAkGU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}