import {
  MatCheckbox,
  MatCheckboxModule
} from "./chunk-GBVM2TU7.js";
import "./chunk-TCMHSQZD.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-DKEYCCVZ.js";
import {
  MatSnackBar
} from "./chunk-3EIBYAGU.js";
import "./chunk-W6VU2H2S.js";
import {
  Mat<PERSON>rror,
  MatFormField,
  MatLabel,
  MatSuffix
} from "./chunk-R3ISBMK2.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-D2SQUTFC.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-L52YVPND.js";
import {
  AuthService
} from "./chunk-EDH5VTX4.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardSubtitle,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-5XTAYFTV.js";
import {
  Router,
  RouterLink,
  RouterModule,
  RouterOutlet
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  NgModule,
  finalize,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-JTRMQXMJ.js";

// src/app/features/auth/components/login/login.component.ts
function LoginComponent_mat_error_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Username is required ");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_error_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password is required ");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_error_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password must be at least 6 characters ");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_spinner_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "mat-spinner", 16);
  }
}
function LoginComponent_span_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Sign In");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_span_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Signing In...");
    \u0275\u0275elementEnd();
  }
}
var LoginComponent = class _LoginComponent {
  fb;
  authService;
  router;
  snackBar;
  loginForm;
  hidePassword = true;
  isLoading = false;
  constructor(fb, authService, router, snackBar) {
    this.fb = fb;
    this.authService = authService;
    this.router = router;
    this.snackBar = snackBar;
  }
  ngOnInit() {
    this.initializeForm();
  }
  initializeForm() {
    this.loginForm = this.fb.group({
      userName: ["", [Validators.required]],
      password: ["", [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }
  onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const loginData = {
        userName: this.loginForm.value.userName,
        password: this.loginForm.value.password
      };
      this.authService.login(loginData).pipe(finalize(() => this.isLoading = false)).subscribe({
        next: (response) => {
          if (response.IsAuthSuccessful) {
            this.snackBar.open("Login successful!", "Close", {
              duration: 3e3,
              panelClass: ["success-snackbar"]
            });
            const dashboardRoute = this.authService.getDashboardRoute();
            this.router.navigate([dashboardRoute]);
          } else {
            this.snackBar.open(response.ErrorMessage || "Login failed", "Close", {
              duration: 5e3,
              panelClass: ["error-snackbar"]
            });
          }
        },
        error: (error) => {
          console.error("Login error:", error);
          this.snackBar.open("An error occurred during login. Please try again.", "Close", {
            duration: 5e3,
            panelClass: ["error-snackbar"]
          });
        }
      });
    }
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 38, vars: 12, consts: [[1, "login-card"], [1, "login-form", 3, "ngSubmit", "formGroup"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "userName", "placeholder", "Enter your username or email", "autocomplete", "username"], ["matSuffix", ""], [4, "ngIf"], ["matInput", "", "formControlName", "password", "placeholder", "Enter your password", "autocomplete", "current-password", 3, "type"], ["mat-icon-button", "", "matSuffix", "", "type", "button", 3, "click"], [1, "form-options"], ["formControlName", "rememberMe", "color", "primary"], ["href", "#", 1, "forgot-password"], ["mat-raised-button", "", "color", "primary", "type", "submit", 1, "login-button", "full-width", 3, "disabled"], ["diameter", "20", 4, "ngIf"], [1, "card-actions"], [1, "signup-text"], ["routerLink", "/auth/register", 1, "signup-link"], ["diameter", "20"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card", 0)(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Welcome Back");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "mat-card-subtitle");
      \u0275\u0275text(5, "Sign in to your account");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "mat-card-content")(7, "form", 1);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_7_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(8, "mat-form-field", 2)(9, "mat-label");
      \u0275\u0275text(10, "Username or Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(11, "input", 3);
      \u0275\u0275elementStart(12, "mat-icon", 4);
      \u0275\u0275text(13, "person");
      \u0275\u0275elementEnd();
      \u0275\u0275template(14, LoginComponent_mat_error_14_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "mat-form-field", 2)(16, "mat-label");
      \u0275\u0275text(17, "Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(18, "input", 6);
      \u0275\u0275elementStart(19, "button", 7);
      \u0275\u0275listener("click", function LoginComponent_Template_button_click_19_listener() {
        return ctx.hidePassword = !ctx.hidePassword;
      });
      \u0275\u0275elementStart(20, "mat-icon");
      \u0275\u0275text(21);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(22, LoginComponent_mat_error_22_Template, 2, 0, "mat-error", 5)(23, LoginComponent_mat_error_23_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "div", 8)(25, "mat-checkbox", 9);
      \u0275\u0275text(26, " Remember me ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "a", 10);
      \u0275\u0275text(28, "Forgot password?");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(29, "button", 11);
      \u0275\u0275template(30, LoginComponent_mat_spinner_30_Template, 1, 0, "mat-spinner", 12)(31, LoginComponent_span_31_Template, 2, 0, "span", 5)(32, LoginComponent_span_32_Template, 2, 0, "span", 5);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(33, "mat-card-actions", 13)(34, "p", 14);
      \u0275\u0275text(35, " Don't have an account? ");
      \u0275\u0275elementStart(36, "a", 15);
      \u0275\u0275text(37, "Sign up here");
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_6_0;
      let tmp_7_0;
      \u0275\u0275advance(7);
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.loginForm.get("userName")) == null ? null : tmp_1_0.hasError("required"));
      \u0275\u0275advance(4);
      \u0275\u0275property("type", ctx.hidePassword ? "password" : "text");
      \u0275\u0275advance();
      \u0275\u0275attribute("aria-label", "Hide password")("aria-pressed", ctx.hidePassword);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.hidePassword ? "visibility_off" : "visibility");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_6_0 = ctx.loginForm.get("password")) == null ? null : tmp_6_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_7_0 = ctx.loginForm.get("password")) == null ? null : tmp_7_0.hasError("minlength"));
      \u0275\u0275advance(6);
      \u0275\u0275property("disabled", ctx.loginForm.invalid || ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule, RouterLink, MatCardModule, MatCard, MatCardActions, MatCardContent, MatCardHeader, MatCardSubtitle, MatCardTitle, MatFormFieldModule, MatFormField, MatLabel, MatError, MatSuffix, MatInputModule, MatInput, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatCheckboxModule, MatCheckbox, MatProgressSpinnerModule, MatProgressSpinner], styles: ["\n\n.login-card[_ngcontent-%COMP%] {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n}\n.login-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 1.5rem;\n}\n.login-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n.login-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n}\n.login-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.login-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.login-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%] {\n  margin-top: 0.5rem;\n}\n.form-options[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0.5rem 0 1.5rem 0;\n}\n.form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\n  color: #667eea;\n  text-decoration: none;\n  font-size: 0.875rem;\n  transition: color 0.3s ease;\n}\n.form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n.login-button[_ngcontent-%COMP%] {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  transition: all 0.3s ease;\n}\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n.login-button[_ngcontent-%COMP%]:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n.login-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\n  margin-right: 8px;\n}\n.card-actions[_ngcontent-%COMP%] {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n}\n.card-actions[_ngcontent-%COMP%]   .signup-text[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n.card-actions[_ngcontent-%COMP%]   .signup-text[_ngcontent-%COMP%]   .signup-link[_ngcontent-%COMP%] {\n  color: #667eea;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n.card-actions[_ngcontent-%COMP%]   .signup-text[_ngcontent-%COMP%]   .signup-link[_ngcontent-%COMP%]:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n[_nghost-%COMP%]     .success-snackbar {\n  background-color: #4caf50;\n  color: white;\n}\n[_nghost-%COMP%]     .error-snackbar {\n  background-color: #f44336;\n  color: white;\n}\n@media (max-width: 480px) {\n  .form-options[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\n    align-self: flex-end;\n  }\n  .login-button[_ngcontent-%COMP%] {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n}\n.login-form[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out 0.2s both;\n}\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n/*# sourceMappingURL=login.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{ selector: "app-login", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      RouterModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatIconModule,
      MatCheckboxModule,
      MatProgressSpinnerModule
    ], template: `
    <mat-card class="login-card">
      <mat-card-header>
        <mat-card-title>Welcome Back</mat-card-title>
        <mat-card-subtitle>Sign in to your account</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Username or Email</mat-label>
            <input 
              matInput 
              formControlName="userName" 
              placeholder="Enter your username or email"
              autocomplete="username">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="loginForm.get('userName')?.hasError('required')">
              Username is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <input 
              matInput 
              [type]="hidePassword ? 'password' : 'text'" 
              formControlName="password"
              placeholder="Enter your password"
              autocomplete="current-password">
            <button 
              mat-icon-button 
              matSuffix 
              type="button"
              (click)="hidePassword = !hidePassword"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hidePassword">
              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
              Password is required
            </mat-error>
            <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
              Password must be at least 6 characters
            </mat-error>
          </mat-form-field>

          <div class="form-options">
            <mat-checkbox formControlName="rememberMe" color="primary">
              Remember me
            </mat-checkbox>
            <a href="#" class="forgot-password">Forgot password?</a>
          </div>

          <button 
            mat-raised-button 
            color="primary" 
            type="submit" 
            class="login-button full-width"
            [disabled]="loginForm.invalid || isLoading">
            <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
            <span *ngIf="!isLoading">Sign In</span>
            <span *ngIf="isLoading">Signing In...</span>
          </button>
        </form>
      </mat-card-content>
      
      <mat-card-actions class="card-actions">
        <p class="signup-text">
          Don't have an account? 
          <a routerLink="/auth/register" class="signup-link">Sign up here</a>
        </p>
      </mat-card-actions>
    </mat-card>
  `, styles: ["/* src/app/features/auth/components/login/login.component.scss */\n.login-card {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n}\n.login-card mat-card-header {\n  text-align: center;\n  margin-bottom: 1.5rem;\n}\n.login-card mat-card-header mat-card-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n.login-card mat-card-header mat-card-subtitle {\n  color: #666;\n  font-size: 0.9rem;\n}\n.login-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.login-form .full-width {\n  width: 100%;\n}\n.login-form mat-form-field .mat-mdc-form-field-subscript-wrapper {\n  margin-top: 0.5rem;\n}\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0.5rem 0 1.5rem 0;\n}\n.form-options .forgot-password {\n  color: #667eea;\n  text-decoration: none;\n  font-size: 0.875rem;\n  transition: color 0.3s ease;\n}\n.form-options .forgot-password:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n.login-button {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  transition: all 0.3s ease;\n}\n.login-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n.login-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n.login-button mat-spinner {\n  margin-right: 8px;\n}\n.card-actions {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n}\n.card-actions .signup-text {\n  text-align: center;\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n.card-actions .signup-text .signup-link {\n  color: #667eea;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n.card-actions .signup-text .signup-link:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n:host ::ng-deep .success-snackbar {\n  background-color: #4caf50;\n  color: white;\n}\n:host ::ng-deep .error-snackbar {\n  background-color: #f44336;\n  color: white;\n}\n@media (max-width: 480px) {\n  .form-options {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .form-options .forgot-password {\n    align-self: flex-end;\n  }\n  .login-button {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n}\n.login-form {\n  animation: fadeInUp 0.6s ease-out 0.2s both;\n}\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n/*# sourceMappingURL=login.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: Router }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/features/auth/components/login/login.component.ts", lineNumber: 110 });
})();

// src/app/features/auth/components/register/register.component.ts
function RegisterComponent_mat_error_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " First name is required ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Last name is required ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Username is required ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Username must be at least 3 characters ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Email is required ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Please enter a valid email ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_48_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password is required ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_49_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password must be at least 8 characters ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_50_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Password must contain at least one uppercase, lowercase, number and special character ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_58_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Please confirm your password ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_59_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Passwords do not match ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_68_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " You must accept the terms and conditions ");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_spinner_70_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "mat-spinner", 23);
  }
}
function RegisterComponent_span_71_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Create Account");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_span_72_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Creating Account...");
    \u0275\u0275elementEnd();
  }
}
var RegisterComponent = class _RegisterComponent {
  fb;
  authService;
  router;
  snackBar;
  registerForm;
  hidePassword = true;
  hideConfirmPassword = true;
  isLoading = false;
  constructor(fb, authService, router, snackBar) {
    this.fb = fb;
    this.authService = authService;
    this.router = router;
    this.snackBar = snackBar;
  }
  ngOnInit() {
    this.initializeForm();
  }
  initializeForm() {
    this.registerForm = this.fb.group({
      firstName: ["", [Validators.required]],
      lastName: ["", [Validators.required]],
      userName: ["", [Validators.required, Validators.minLength(3)]],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: [""],
      password: ["", [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      ]],
      confirmPassword: ["", [Validators.required]],
      acceptTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
  }
  passwordMatchValidator(control) {
    const password = control.get("password");
    const confirmPassword = control.get("confirmPassword");
    if (!password || !confirmPassword) {
      return null;
    }
    if (password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    } else {
      const errors = confirmPassword.errors;
      if (errors) {
        delete errors["passwordMismatch"];
        if (Object.keys(errors).length === 0) {
          confirmPassword.setErrors(null);
        }
      }
      return null;
    }
  }
  onSubmit() {
    if (this.registerForm.valid) {
      this.isLoading = true;
      const registrationData = {
        userName: this.registerForm.value.userName,
        email: this.registerForm.value.email,
        password: this.registerForm.value.password,
        confirmPassword: this.registerForm.value.confirmPassword,
        firstName: this.registerForm.value.firstName,
        lastName: this.registerForm.value.lastName,
        phoneNumber: this.registerForm.value.phoneNumber || void 0
      };
      this.authService.register(registrationData).pipe(finalize(() => this.isLoading = false)).subscribe({
        next: (response) => {
          if (response.IsAuthSuccessful) {
            this.snackBar.open("Registration successful! Please sign in.", "Close", {
              duration: 5e3,
              panelClass: ["success-snackbar"]
            });
            this.router.navigate(["/auth/login"]);
          } else {
            this.snackBar.open(response.ErrorMessage || "Registration failed", "Close", {
              duration: 5e3,
              panelClass: ["error-snackbar"]
            });
          }
        },
        error: (error) => {
          console.error("Registration error:", error);
          this.snackBar.open("An error occurred during registration. Please try again.", "Close", {
            duration: 5e3,
            panelClass: ["error-snackbar"]
          });
        }
      });
    }
  }
  static \u0275fac = function RegisterComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RegisterComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RegisterComponent, selectors: [["app-register"]], decls: 78, vars: 21, consts: [[1, "register-card"], [1, "register-form", 3, "ngSubmit", "formGroup"], [1, "name-row"], ["appearance", "outline", 1, "half-width"], ["matInput", "", "formControlName", "firstName", "placeholder", "Enter first name", "autocomplete", "given-name"], [4, "ngIf"], ["matInput", "", "formControlName", "lastName", "placeholder", "Enter last name", "autocomplete", "family-name"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "userName", "placeholder", "Choose a username", "autocomplete", "username"], ["matSuffix", ""], ["matInput", "", "formControlName", "email", "placeholder", "Enter your email", "autocomplete", "email"], ["matInput", "", "formControlName", "phoneNumber", "placeholder", "Enter phone number (optional)", "autocomplete", "tel"], ["matInput", "", "formControlName", "password", "placeholder", "Create a password", "autocomplete", "new-password", 3, "type"], ["mat-icon-button", "", "matSuffix", "", "type", "button", 3, "click"], ["matInput", "", "formControlName", "confirmPassword", "placeholder", "Confirm your password", "autocomplete", "new-password", 3, "type"], [1, "terms-section"], ["formControlName", "acceptTerms", "color", "primary"], ["href", "#", 1, "terms-link"], ["mat-raised-button", "", "color", "primary", "type", "submit", 1, "register-button", "full-width", 3, "disabled"], ["diameter", "20", 4, "ngIf"], [1, "card-actions"], [1, "signin-text"], ["routerLink", "/auth/login", 1, "signin-link"], ["diameter", "20"]], template: function RegisterComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card", 0)(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Create Account");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "mat-card-subtitle");
      \u0275\u0275text(5, "Join our platform today");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "mat-card-content")(7, "form", 1);
      \u0275\u0275listener("ngSubmit", function RegisterComponent_Template_form_ngSubmit_7_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(8, "div", 2)(9, "mat-form-field", 3)(10, "mat-label");
      \u0275\u0275text(11, "First Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(12, "input", 4);
      \u0275\u0275template(13, RegisterComponent_mat_error_13_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "mat-form-field", 3)(15, "mat-label");
      \u0275\u0275text(16, "Last Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(17, "input", 6);
      \u0275\u0275template(18, RegisterComponent_mat_error_18_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(19, "mat-form-field", 7)(20, "mat-label");
      \u0275\u0275text(21, "Username");
      \u0275\u0275elementEnd();
      \u0275\u0275element(22, "input", 8);
      \u0275\u0275elementStart(23, "mat-icon", 9);
      \u0275\u0275text(24, "person");
      \u0275\u0275elementEnd();
      \u0275\u0275template(25, RegisterComponent_mat_error_25_Template, 2, 0, "mat-error", 5)(26, RegisterComponent_mat_error_26_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "mat-form-field", 7)(28, "mat-label");
      \u0275\u0275text(29, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(30, "input", 10);
      \u0275\u0275elementStart(31, "mat-icon", 9);
      \u0275\u0275text(32, "email");
      \u0275\u0275elementEnd();
      \u0275\u0275template(33, RegisterComponent_mat_error_33_Template, 2, 0, "mat-error", 5)(34, RegisterComponent_mat_error_34_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "mat-form-field", 7)(36, "mat-label");
      \u0275\u0275text(37, "Phone Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(38, "input", 11);
      \u0275\u0275elementStart(39, "mat-icon", 9);
      \u0275\u0275text(40, "phone");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "mat-form-field", 7)(42, "mat-label");
      \u0275\u0275text(43, "Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(44, "input", 12);
      \u0275\u0275elementStart(45, "button", 13);
      \u0275\u0275listener("click", function RegisterComponent_Template_button_click_45_listener() {
        return ctx.hidePassword = !ctx.hidePassword;
      });
      \u0275\u0275elementStart(46, "mat-icon");
      \u0275\u0275text(47);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(48, RegisterComponent_mat_error_48_Template, 2, 0, "mat-error", 5)(49, RegisterComponent_mat_error_49_Template, 2, 0, "mat-error", 5)(50, RegisterComponent_mat_error_50_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "mat-form-field", 7)(52, "mat-label");
      \u0275\u0275text(53, "Confirm Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(54, "input", 14);
      \u0275\u0275elementStart(55, "button", 13);
      \u0275\u0275listener("click", function RegisterComponent_Template_button_click_55_listener() {
        return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;
      });
      \u0275\u0275elementStart(56, "mat-icon");
      \u0275\u0275text(57);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(58, RegisterComponent_mat_error_58_Template, 2, 0, "mat-error", 5)(59, RegisterComponent_mat_error_59_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "div", 15)(61, "mat-checkbox", 16);
      \u0275\u0275text(62, " I agree to the ");
      \u0275\u0275elementStart(63, "a", 17);
      \u0275\u0275text(64, "Terms of Service");
      \u0275\u0275elementEnd();
      \u0275\u0275text(65, " and ");
      \u0275\u0275elementStart(66, "a", 17);
      \u0275\u0275text(67, "Privacy Policy");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(68, RegisterComponent_mat_error_68_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "button", 18);
      \u0275\u0275template(70, RegisterComponent_mat_spinner_70_Template, 1, 0, "mat-spinner", 19)(71, RegisterComponent_span_71_Template, 2, 0, "span", 5)(72, RegisterComponent_span_72_Template, 2, 0, "span", 5);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(73, "mat-card-actions", 20)(74, "p", 21);
      \u0275\u0275text(75, " Already have an account? ");
      \u0275\u0275elementStart(76, "a", 22);
      \u0275\u0275text(77, "Sign in here");
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_2_0;
      let tmp_3_0;
      let tmp_4_0;
      let tmp_5_0;
      let tmp_6_0;
      let tmp_9_0;
      let tmp_10_0;
      let tmp_11_0;
      let tmp_14_0;
      let tmp_15_0;
      let tmp_16_0;
      \u0275\u0275advance(7);
      \u0275\u0275property("formGroup", ctx.registerForm);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.registerForm.get("firstName")) == null ? null : tmp_1_0.hasError("required"));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_2_0 = ctx.registerForm.get("lastName")) == null ? null : tmp_2_0.hasError("required"));
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", (tmp_3_0 = ctx.registerForm.get("userName")) == null ? null : tmp_3_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_4_0 = ctx.registerForm.get("userName")) == null ? null : tmp_4_0.hasError("minlength"));
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", (tmp_5_0 = ctx.registerForm.get("email")) == null ? null : tmp_5_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_6_0 = ctx.registerForm.get("email")) == null ? null : tmp_6_0.hasError("email"));
      \u0275\u0275advance(10);
      \u0275\u0275property("type", ctx.hidePassword ? "password" : "text");
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate(ctx.hidePassword ? "visibility_off" : "visibility");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_9_0 = ctx.registerForm.get("password")) == null ? null : tmp_9_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_10_0 = ctx.registerForm.get("password")) == null ? null : tmp_10_0.hasError("minlength"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_11_0 = ctx.registerForm.get("password")) == null ? null : tmp_11_0.hasError("pattern"));
      \u0275\u0275advance(4);
      \u0275\u0275property("type", ctx.hideConfirmPassword ? "password" : "text");
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate(ctx.hideConfirmPassword ? "visibility_off" : "visibility");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_14_0 = ctx.registerForm.get("confirmPassword")) == null ? null : tmp_14_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_15_0 = ctx.registerForm.get("confirmPassword")) == null ? null : tmp_15_0.hasError("passwordMismatch"));
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", ((tmp_16_0 = ctx.registerForm.get("acceptTerms")) == null ? null : tmp_16_0.hasError("required")) && ((tmp_16_0 = ctx.registerForm.get("acceptTerms")) == null ? null : tmp_16_0.touched));
      \u0275\u0275advance();
      \u0275\u0275property("disabled", ctx.registerForm.invalid || ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule, RouterLink, MatCardModule, MatCard, MatCardActions, MatCardContent, MatCardHeader, MatCardSubtitle, MatCardTitle, MatFormFieldModule, MatFormField, MatLabel, MatError, MatSuffix, MatInputModule, MatInput, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatCheckboxModule, MatCheckbox, MatProgressSpinnerModule, MatProgressSpinner], styles: ["\n\n.register-card[_ngcontent-%COMP%] {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n}\n.register-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 1.5rem;\n}\n.register-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n.register-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n}\n.register-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.register-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.register-form[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\n  width: calc(50% - 0.5rem);\n}\n.register-form[_ngcontent-%COMP%]   .name-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-mdc-form-field-subscript-wrapper[_ngcontent-%COMP%] {\n  margin-top: 0.5rem;\n}\n.terms-section[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 1.5rem 0;\n}\n.terms-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n}\n.terms-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\n  line-height: 1.4;\n}\n.terms-section[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%] {\n  color: #667eea;\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n.terms-section[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%]:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n.terms-section[_ngcontent-%COMP%]   mat-error[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #f44336;\n  margin-top: 0.5rem;\n  display: block;\n}\n.register-button[_ngcontent-%COMP%] {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  transition: all 0.3s ease;\n}\n.register-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n.register-button[_ngcontent-%COMP%]:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n.register-button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\n  margin-right: 8px;\n}\n.card-actions[_ngcontent-%COMP%] {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n}\n.card-actions[_ngcontent-%COMP%]   .signin-text[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n.card-actions[_ngcontent-%COMP%]   .signin-text[_ngcontent-%COMP%]   .signin-link[_ngcontent-%COMP%] {\n  color: #667eea;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n.card-actions[_ngcontent-%COMP%]   .signin-text[_ngcontent-%COMP%]   .signin-link[_ngcontent-%COMP%]:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n[_nghost-%COMP%]     .success-snackbar {\n  background-color: #4caf50;\n  color: white;\n}\n[_nghost-%COMP%]     .error-snackbar {\n  background-color: #f44336;\n  color: white;\n}\n@media (max-width: 768px) {\n  .name-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n}\n@media (max-width: 480px) {\n  .register-button[_ngcontent-%COMP%] {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n  .terms-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%] {\n    font-size: 0.8rem;\n  }\n}\n.register-form[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out 0.2s both;\n}\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.password-strength[_ngcontent-%COMP%] {\n  margin-top: 0.5rem;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%] {\n  height: 4px;\n  background-color: #e0e0e0;\n  border-radius: 2px;\n  overflow: hidden;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%] {\n  height: 100%;\n  transition: all 0.3s ease;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.weak[_ngcontent-%COMP%] {\n  width: 33%;\n  background-color: #f44336;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.medium[_ngcontent-%COMP%] {\n  width: 66%;\n  background-color: #ff9800;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.strong[_ngcontent-%COMP%] {\n  width: 100%;\n  background-color: #4caf50;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-text.weak[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-text.medium[_ngcontent-%COMP%] {\n  color: #ff9800;\n}\n.password-strength[_ngcontent-%COMP%]   .strength-text.strong[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n/*# sourceMappingURL=register.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RegisterComponent, [{
    type: Component,
    args: [{ selector: "app-register", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      RouterModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatIconModule,
      MatCheckboxModule,
      MatProgressSpinnerModule
    ], template: `
    <mat-card class="register-card">
      <mat-card-header>
        <mat-card-title>Create Account</mat-card-title>
        <mat-card-subtitle>Join our platform today</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
          <div class="name-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>First Name</mat-label>
              <input 
                matInput 
                formControlName="firstName" 
                placeholder="Enter first name"
                autocomplete="given-name">
              <mat-error *ngIf="registerForm.get('firstName')?.hasError('required')">
                First name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Last Name</mat-label>
              <input 
                matInput 
                formControlName="lastName" 
                placeholder="Enter last name"
                autocomplete="family-name">
              <mat-error *ngIf="registerForm.get('lastName')?.hasError('required')">
                Last name is required
              </mat-error>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Username</mat-label>
            <input 
              matInput 
              formControlName="userName" 
              placeholder="Choose a username"
              autocomplete="username">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="registerForm.get('userName')?.hasError('required')">
              Username is required
            </mat-error>
            <mat-error *ngIf="registerForm.get('userName')?.hasError('minlength')">
              Username must be at least 3 characters
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Email</mat-label>
            <input 
              matInput 
              formControlName="email" 
              placeholder="Enter your email"
              autocomplete="email">
            <mat-icon matSuffix>email</mat-icon>
            <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
              Email is required
            </mat-error>
            <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
              Please enter a valid email
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Phone Number</mat-label>
            <input 
              matInput 
              formControlName="phoneNumber" 
              placeholder="Enter phone number (optional)"
              autocomplete="tel">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <input 
              matInput 
              [type]="hidePassword ? 'password' : 'text'" 
              formControlName="password"
              placeholder="Create a password"
              autocomplete="new-password">
            <button 
              mat-icon-button 
              matSuffix 
              type="button"
              (click)="hidePassword = !hidePassword">
              <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
              Password is required
            </mat-error>
            <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
              Password must be at least 8 characters
            </mat-error>
            <mat-error *ngIf="registerForm.get('password')?.hasError('pattern')">
              Password must contain at least one uppercase, lowercase, number and special character
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Confirm Password</mat-label>
            <input 
              matInput 
              [type]="hideConfirmPassword ? 'password' : 'text'" 
              formControlName="confirmPassword"
              placeholder="Confirm your password"
              autocomplete="new-password">
            <button 
              mat-icon-button 
              matSuffix 
              type="button"
              (click)="hideConfirmPassword = !hideConfirmPassword">
              <mat-icon>{{hideConfirmPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('required')">
              Please confirm your password
            </mat-error>
            <mat-error *ngIf="registerForm.get('confirmPassword')?.hasError('passwordMismatch')">
              Passwords do not match
            </mat-error>
          </mat-form-field>

          <div class="terms-section">
            <mat-checkbox formControlName="acceptTerms" color="primary">
              I agree to the <a href="#" class="terms-link">Terms of Service</a> and 
              <a href="#" class="terms-link">Privacy Policy</a>
            </mat-checkbox>
            <mat-error *ngIf="registerForm.get('acceptTerms')?.hasError('required') && registerForm.get('acceptTerms')?.touched">
              You must accept the terms and conditions
            </mat-error>
          </div>

          <button 
            mat-raised-button 
            color="primary" 
            type="submit" 
            class="register-button full-width"
            [disabled]="registerForm.invalid || isLoading">
            <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
            <span *ngIf="!isLoading">Create Account</span>
            <span *ngIf="isLoading">Creating Account...</span>
          </button>
        </form>
      </mat-card-content>
      
      <mat-card-actions class="card-actions">
        <p class="signin-text">
          Already have an account? 
          <a routerLink="/auth/login" class="signin-link">Sign in here</a>
        </p>
      </mat-card-actions>
    </mat-card>
  `, styles: ["/* src/app/features/auth/components/register/register.component.scss */\n.register-card {\n  width: 100%;\n  border: none;\n  box-shadow: none;\n  background: transparent;\n}\n.register-card mat-card-header {\n  text-align: center;\n  margin-bottom: 1.5rem;\n}\n.register-card mat-card-header mat-card-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n}\n.register-card mat-card-header mat-card-subtitle {\n  color: #666;\n  font-size: 0.9rem;\n}\n.register-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n.register-form .full-width {\n  width: 100%;\n}\n.register-form .half-width {\n  width: calc(50% - 0.5rem);\n}\n.register-form .name-row {\n  display: flex;\n  gap: 1rem;\n}\n.register-form mat-form-field .mat-mdc-form-field-subscript-wrapper {\n  margin-top: 0.5rem;\n}\n.terms-section {\n  margin: 0.5rem 0 1.5rem 0;\n}\n.terms-section mat-checkbox {\n  font-size: 0.875rem;\n}\n.terms-section mat-checkbox .mat-checkbox-label {\n  line-height: 1.4;\n}\n.terms-section .terms-link {\n  color: #667eea;\n  text-decoration: none;\n  transition: color 0.3s ease;\n}\n.terms-section .terms-link:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n.terms-section mat-error {\n  font-size: 0.75rem;\n  color: #f44336;\n  margin-top: 0.5rem;\n  display: block;\n}\n.register-button {\n  height: 48px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  transition: all 0.3s ease;\n}\n.register-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n.register-button:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n}\n.register-button mat-spinner {\n  margin-right: 8px;\n}\n.card-actions {\n  padding: 1rem 0 0 0;\n  justify-content: center;\n}\n.card-actions .signin-text {\n  text-align: center;\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n.card-actions .signin-text .signin-link {\n  color: #667eea;\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.3s ease;\n}\n.card-actions .signin-text .signin-link:hover {\n  color: #764ba2;\n  text-decoration: underline;\n}\n:host ::ng-deep .success-snackbar {\n  background-color: #4caf50;\n  color: white;\n}\n:host ::ng-deep .error-snackbar {\n  background-color: #f44336;\n  color: white;\n}\n@media (max-width: 768px) {\n  .name-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .name-row .half-width {\n    width: 100%;\n  }\n}\n@media (max-width: 480px) {\n  .register-button {\n    height: 44px;\n    font-size: 0.9rem;\n  }\n  .terms-section mat-checkbox {\n    font-size: 0.8rem;\n  }\n}\n.register-form {\n  animation: fadeInUp 0.6s ease-out 0.2s both;\n}\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.password-strength {\n  margin-top: 0.5rem;\n}\n.password-strength .strength-bar {\n  height: 4px;\n  background-color: #e0e0e0;\n  border-radius: 2px;\n  overflow: hidden;\n}\n.password-strength .strength-bar .strength-fill {\n  height: 100%;\n  transition: all 0.3s ease;\n}\n.password-strength .strength-bar .strength-fill.weak {\n  width: 33%;\n  background-color: #f44336;\n}\n.password-strength .strength-bar .strength-fill.medium {\n  width: 66%;\n  background-color: #ff9800;\n}\n.password-strength .strength-bar .strength-fill.strong {\n  width: 100%;\n  background-color: #4caf50;\n}\n.password-strength .strength-text {\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n}\n.password-strength .strength-text.weak {\n  color: #f44336;\n}\n.password-strength .strength-text.medium {\n  color: #ff9800;\n}\n.password-strength .strength-text.strong {\n  color: #4caf50;\n}\n/*# sourceMappingURL=register.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: Router }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RegisterComponent, { className: "RegisterComponent", filePath: "src/app/features/auth/components/register/register.component.ts", lineNumber: 192 });
})();

// src/app/features/auth/components/auth-layout/auth-layout.component.ts
var AuthLayoutComponent = class _AuthLayoutComponent {
  static \u0275fac = function AuthLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthLayoutComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AuthLayoutComponent, selectors: [["app-auth-layout"]], decls: 14, vars: 0, consts: [[1, "auth-container"], [1, "auth-background"], [1, "auth-overlay"], [1, "auth-content"], [1, "auth-card-container"], [1, "brand-section"], [1, "brand-logo"], [1, "brand-icon"], [1, "brand-title"], [1, "brand-subtitle"]], template: function AuthLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
      \u0275\u0275element(2, "div", 2);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 3)(4, "div", 4)(5, "div", 5)(6, "div", 6)(7, "mat-icon", 7);
      \u0275\u0275text(8, "business");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "h1", 8);
      \u0275\u0275text(10, "BPM Light");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "p", 9);
      \u0275\u0275text(12, "Intelligent Business Process Management Platform");
      \u0275\u0275elementEnd()();
      \u0275\u0275element(13, "router-outlet");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    RouterOutlet,
    MatIconModule,
    MatIcon
  ], styles: ['\n\n.auth-container[_ngcontent-%COMP%] {\n  height: 100vh;\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n.auth-background[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.auth-background[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(\n      circle at 20% 80%,\n      rgba(120, 119, 198, 0.3) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 80% 20%,\n      rgba(255, 255, 255, 0.1) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 40% 40%,\n      rgba(120, 119, 198, 0.2) 0%,\n      transparent 50%);\n}\n.auth-overlay[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.1);\n}\n.auth-content[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n}\n.auth-card-container[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border-radius: 16px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\n}\n.brand-section[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.brand-section[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.brand-section[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  color: #667eea;\n}\n.brand-section[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.brand-section[_ngcontent-%COMP%]   .brand-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n@keyframes _ngcontent-%COMP%_slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@media (max-width: 768px) {\n  .auth-content[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .auth-card-container[_ngcontent-%COMP%] {\n    padding: 1.5rem;\n    max-width: 100%;\n  }\n  .brand-section[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%] {\n    font-size: 1.75rem;\n  }\n  .brand-section[_ngcontent-%COMP%]   .brand-subtitle[_ngcontent-%COMP%] {\n    font-size: 0.85rem;\n  }\n}\n@media (max-width: 480px) {\n  .auth-card-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  .brand-section[_ngcontent-%COMP%] {\n    margin-bottom: 1.5rem;\n  }\n  .brand-section[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\n    font-size: 40px;\n    width: 40px;\n    height: 40px;\n  }\n  .brand-section[_ngcontent-%COMP%]   .brand-title[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=auth-layout.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-auth-layout", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatIconModule
    ], template: `
    <div class="auth-container">
      <div class="auth-background">
        <div class="auth-overlay"></div>
      </div>
      <div class="auth-content">
        <div class="auth-card-container">
          <div class="brand-section">
            <div class="brand-logo">
              <mat-icon class="brand-icon">business</mat-icon>
            </div>
            <h1 class="brand-title">BPM Light</h1>
            <p class="brand-subtitle">Intelligent Business Process Management Platform</p>
          </div>
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>
  `, styles: ['/* src/app/features/auth/components/auth-layout/auth-layout.component.scss */\n.auth-container {\n  height: 100vh;\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n.auth-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.auth-background::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image:\n    radial-gradient(\n      circle at 20% 80%,\n      rgba(120, 119, 198, 0.3) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 80% 20%,\n      rgba(255, 255, 255, 0.1) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 40% 40%,\n      rgba(120, 119, 198, 0.2) 0%,\n      transparent 50%);\n}\n.auth-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.1);\n}\n.auth-content {\n  position: relative;\n  z-index: 1;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n}\n.auth-card-container {\n  width: 100%;\n  max-width: 400px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border-radius: 16px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  animation: slideUp 0.6s ease-out;\n}\n.brand-section {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.brand-section .brand-logo {\n  margin-bottom: 1rem;\n}\n.brand-section .brand-logo .brand-icon {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  color: #667eea;\n}\n.brand-section .brand-title {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.brand-section .brand-subtitle {\n  color: #666;\n  font-size: 0.9rem;\n  margin: 0;\n}\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@media (max-width: 768px) {\n  .auth-content {\n    padding: 1rem;\n  }\n  .auth-card-container {\n    padding: 1.5rem;\n    max-width: 100%;\n  }\n  .brand-section .brand-title {\n    font-size: 1.75rem;\n  }\n  .brand-section .brand-subtitle {\n    font-size: 0.85rem;\n  }\n}\n@media (max-width: 480px) {\n  .auth-card-container {\n    padding: 1rem;\n    border-radius: 12px;\n  }\n  .brand-section {\n    margin-bottom: 1.5rem;\n  }\n  .brand-section .brand-logo .brand-icon {\n    font-size: 40px;\n    width: 40px;\n    height: 40px;\n  }\n  .brand-section .brand-title {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=auth-layout.component.css.map */\n'] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AuthLayoutComponent, { className: "AuthLayoutComponent", filePath: "src/app/features/auth/components/auth-layout/auth-layout.component.ts", lineNumber: 35 });
})();

// src/app/features/auth/auth.module.ts
var routes = [
  {
    path: "",
    component: AuthLayoutComponent,
    children: [
      { path: "", redirectTo: "login", pathMatch: "full" },
      { path: "login", component: LoginComponent },
      { path: "register", component: RegisterComponent }
    ]
  }
];
var AuthModule = class _AuthModule {
  static \u0275fac = function AuthModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _AuthModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
export {
  AuthModule
};
//# sourceMappingURL=chunk-C5TZTRJM.js.map
