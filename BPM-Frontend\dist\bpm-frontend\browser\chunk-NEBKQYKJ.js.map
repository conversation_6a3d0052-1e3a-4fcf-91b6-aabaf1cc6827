{"version": 3, "sources": ["src/app/shared/components/icon-test/icon-test.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\n\n@Component({\n  selector: 'app-icon-test',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatIconModule,\n    MatButtonModule,\n    MatCardModule\n  ],\n  template: `\n    <div class=\"icon-test-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Icon Test Page</mat-card-title>\n          <mat-card-subtitle>Testing Material Icons across different platforms</mat-card-subtitle>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <div class=\"icon-grid\">\n            <div class=\"icon-section\">\n              <h3>Material Icons (Standard)</h3>\n              <div class=\"icon-row\">\n                <mat-icon>home</mat-icon>\n                <mat-icon>dashboard</mat-icon>\n                <mat-icon>person</mat-icon>\n                <mat-icon>settings</mat-icon>\n                <mat-icon>notifications</mat-icon>\n                <mat-icon>business</mat-icon>\n                <mat-icon>assignment</mat-icon>\n                <mat-icon>account_circle</mat-icon>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Material Icons in Buttons</h3>\n              <div class=\"button-row\">\n                <button mat-raised-button color=\"primary\">\n                  <mat-icon>add</mat-icon>\n                  Add New\n                </button>\n                <button mat-raised-button color=\"accent\">\n                  <mat-icon>edit</mat-icon>\n                  Edit\n                </button>\n                <button mat-raised-button color=\"warn\">\n                  <mat-icon>delete</mat-icon>\n                  Delete\n                </button>\n                <button mat-icon-button>\n                  <mat-icon>more_vert</mat-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Navigation Icons</h3>\n              <div class=\"icon-row\">\n                <mat-icon>menu</mat-icon>\n                <mat-icon>arrow_back</mat-icon>\n                <mat-icon>arrow_forward</mat-icon>\n                <mat-icon>expand_more</mat-icon>\n                <mat-icon>expand_less</mat-icon>\n                <mat-icon>close</mat-icon>\n                <mat-icon>search</mat-icon>\n                <mat-icon>filter_list</mat-icon>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Status Icons</h3>\n              <div class=\"icon-row\">\n                <mat-icon class=\"success-icon\">check_circle</mat-icon>\n                <mat-icon class=\"warning-icon\">warning</mat-icon>\n                <mat-icon class=\"error-icon\">error</mat-icon>\n                <mat-icon class=\"info-icon\">info</mat-icon>\n                <mat-icon>pending</mat-icon>\n                <mat-icon>schedule</mat-icon>\n                <mat-icon>done</mat-icon>\n                <mat-icon>cancel</mat-icon>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Business Process Icons</h3>\n              <div class=\"icon-row\">\n                <mat-icon>work</mat-icon>\n                <mat-icon>assignment_turned_in</mat-icon>\n                <mat-icon>approval</mat-icon>\n                <mat-icon>supervisor_account</mat-icon>\n                <mat-icon>people</mat-icon>\n                <mat-icon>analytics</mat-icon>\n                <mat-icon>account_tree</mat-icon>\n                <mat-icon>timeline</mat-icon>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Different Sizes</h3>\n              <div class=\"icon-row\">\n                <mat-icon class=\"icon-18\">home</mat-icon>\n                <mat-icon class=\"icon-24\">home</mat-icon>\n                <mat-icon class=\"icon-36\">home</mat-icon>\n                <mat-icon class=\"icon-48\">home</mat-icon>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>Raw HTML Icons (Fallback Test)</h3>\n              <div class=\"icon-row\">\n                <i class=\"material-icons\">home</i>\n                <i class=\"material-icons\">dashboard</i>\n                <i class=\"material-icons\">person</i>\n                <i class=\"material-icons\">settings</i>\n              </div>\n            </div>\n\n            <div class=\"icon-section\">\n              <h3>MDI Icons (Alternative)</h3>\n              <div class=\"icon-row\">\n                <i class=\"mdi mdi-home\"></i>\n                <i class=\"mdi mdi-view-dashboard\"></i>\n                <i class=\"mdi mdi-account\"></i>\n                <i class=\"mdi mdi-cog\"></i>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .icon-test-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .icon-grid {\n      display: flex;\n      flex-direction: column;\n      gap: 30px;\n    }\n\n    .icon-section {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      padding: 20px;\n      background: #fafafa;\n    }\n\n    .icon-section h3 {\n      margin: 0 0 15px 0;\n      color: #333;\n      font-weight: 500;\n    }\n\n    .icon-row {\n      display: flex;\n      gap: 20px;\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .button-row {\n      display: flex;\n      gap: 15px;\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    mat-icon {\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    .icon-18 {\n      font-size: 18px !important;\n      width: 18px !important;\n      height: 18px !important;\n    }\n\n    .icon-24 {\n      font-size: 24px !important;\n      width: 24px !important;\n      height: 24px !important;\n    }\n\n    .icon-36 {\n      font-size: 36px !important;\n      width: 36px !important;\n      height: 36px !important;\n    }\n\n    .icon-48 {\n      font-size: 48px !important;\n      width: 48px !important;\n      height: 48px !important;\n    }\n\n    .success-icon {\n      color: #4caf50;\n    }\n\n    .warning-icon {\n      color: #ff9800;\n    }\n\n    .error-icon {\n      color: #f44336;\n    }\n\n    .info-icon {\n      color: #2196f3;\n    }\n\n    .material-icons {\n      font-size: 24px;\n      color: #666;\n    }\n\n    .mdi {\n      font-size: 24px;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .icon-row,\n      .button-row {\n        gap: 10px;\n      }\n      \n      .icon-test-container {\n        padding: 10px;\n      }\n    }\n  `]\n})\nexport class IconTestComponent { }"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoPM,IAAO,oBAAP,MAAO,mBAAiB;;qCAAjB,oBAAiB;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,KAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,QAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,MAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,UAAA,GAAA,CAAA,GAAA,OAAA,oBAAA,GAAA,CAAA,GAAA,OAAA,aAAA,GAAA,CAAA,GAAA,OAAA,SAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApO1B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,UAAA,EACrB,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA;AAC9B,MAAA,yBAAA,GAAA,mBAAA;AAAmB,MAAA,iBAAA,GAAA,mDAAA;AAAiD,MAAA,uBAAA,EAAoB;AAG1F,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACO,GAAA,OAAA,CAAA,EACK,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,UAAA;AACV,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAW,EAC/B;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,UAAA,CAAA,EACoB,IAAA,UAAA;AAC9B,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,WAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAAyC,IAAA,UAAA;AAC7B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,QAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAAuC,IAAA,UAAA;AAC3B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAAwB,IAAA,UAAA;AACZ,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAW,EACvB,EACL;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,UAAA;AACV,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACf,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAW,EAC5B;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAChB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,YAAA,CAAA;AACW,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAC3C,MAAA,yBAAA,IAAA,YAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACtC,MAAA,yBAAA,IAAA,YAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,YAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAW,EACvB;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA;AAC1B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,UAAA;AACV,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,WAAA;AAAS,MAAA,uBAAA;AACnB,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,cAAA;AAAY,MAAA,uBAAA;AACtB,MAAA,yBAAA,KAAA,UAAA;AAAU,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA,EAAW,EACzB;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA0B,KAAA,IAAA;AACpB,MAAA,iBAAA,KAAA,iBAAA;AAAe,MAAA,uBAAA;AACnB,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAsB,KAAA,YAAA,EAAA;AACM,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,KAAA,YAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,KAAA,YAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,KAAA,YAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA,EAAW,EACrC;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA0B,KAAA,IAAA;AACpB,MAAA,iBAAA,KAAA,gCAAA;AAA8B,MAAA,uBAAA;AAClC,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAsB,KAAA,KAAA,EAAA;AACM,MAAA,iBAAA,KAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,KAAA,KAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA,EAAI,EAClC;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAA0B,KAAA,IAAA;AACpB,MAAA,iBAAA,KAAA,yBAAA;AAAuB,MAAA,uBAAA;AAC3B,MAAA,yBAAA,KAAA,OAAA,CAAA;AACE,MAAA,oBAAA,KAAA,KAAA,EAAA,EAA4B,KAAA,KAAA,EAAA,EACU,KAAA,KAAA,EAAA,EACP,KAAA,KAAA,EAAA;AAEjC,MAAA,uBAAA,EAAM,EACF,EACF,EACW,EACV;;;IA3Hb;IACA;IAAa;IACb;IAAe;IAAA;IACf;IAAa;IAAA;IAAA;IAAA;IAAA;EAAA,GAAA,QAAA,CAAA,60DAAA,EAAA,CAAA;;;sEAuOJ,mBAAiB,CAAA;UA9O7B;uBACW,iBAAe,YACb,MAAI,SACP;MACP;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwHT,QAAA,CAAA,woDAAA,EAAA,CAAA;;;;6EA6GU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,8DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}