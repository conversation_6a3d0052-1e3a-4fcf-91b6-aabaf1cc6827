{"version": 3, "sources": ["src/app/features/dashboard/components/hr-dashboard/hr-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { RequestService } from '../../../../core/services/request.service';\nimport { RequestDto, RequestStatus, RequestType } from '../../../../core/models';\n\ninterface HRMetrics {\n  totalEmployees: number;\n  activeRequests: number;\n  processedThisMonth: number;\n  averageProcessingTime: number;\n  leaveRequests: number;\n  expenseReports: number;\n  trainingRequests: number;\n}\n\n@Component({\n  selector: 'app-hr-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatProgressBarModule\n  ],\n  template: `\n    <div class=\"hr-dashboard\">\n      <div class=\"dashboard-header\">\n        <h1>HR Dashboard</h1>\n        <p>Manage employee requests and HR processes</p>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"metrics-grid\">\n        <mat-card class=\"metric-card employees\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>people</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{hrMetrics.totalEmployees}}</h3>\n                <p>Total Employees</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card active\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>assignment</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{hrMetrics.activeRequests}}</h3>\n                <p>Active Requests</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card processed\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>done_all</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{hrMetrics.processedThisMonth}}</h3>\n                <p>Processed This Month</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card time\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>schedule</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{hrMetrics.averageProcessingTime}}h</h3>\n                <p>Avg. Processing Time</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Request Type Breakdown -->\n      <div class=\"request-types-grid\">\n        <mat-card class=\"type-card leave\">\n          <mat-card-content>\n            <div class=\"type-metric\">\n              <mat-icon>event_available</mat-icon>\n              <div class=\"type-info\">\n                <h4>{{hrMetrics.leaveRequests}}</h4>\n                <p>Leave Requests</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"type-card expense\">\n          <mat-card-content>\n            <div class=\"type-metric\">\n              <mat-icon>receipt</mat-icon>\n              <div class=\"type-info\">\n                <h4>{{hrMetrics.expenseReports}}</h4>\n                <p>Expense Reports</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"type-card training\">\n          <mat-card-content>\n            <div class=\"type-metric\">\n              <mat-icon>school</mat-icon>\n              <div class=\"type-info\">\n                <h4>{{hrMetrics.trainingRequests}}</h4>\n                <p>Training Requests</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Main Content Tabs -->\n      <mat-card class=\"content-card\">\n        <mat-tab-group>\n          <!-- Pending HR Review -->\n          <mat-tab label=\"Pending Review ({{pendingHRRequests.length}})\">\n            <div class=\"tab-content\">\n              <div class=\"tab-header\">\n                <h3>Requests Awaiting HR Review</h3>\n                <button mat-raised-button color=\"primary\" routerLink=\"/requests\">\n                  <mat-icon>list</mat-icon>\n                  View All Requests\n                </button>\n              </div>\n\n              <div *ngIf=\"pendingHRRequests.length > 0\" class=\"requests-table\">\n                <table mat-table [dataSource]=\"pendingHRRequests\">\n                  <!-- Employee Column -->\n                  <ng-container matColumnDef=\"employee\">\n                    <th mat-header-cell *matHeaderCellDef>Employee</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"employee-info\">\n                        <strong>{{request.initiatorName}}</strong>\n                        <small>{{request.createdAt | date:'short'}}</small>\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <!-- Request Type Column -->\n                  <ng-container matColumnDef=\"type\">\n                    <th mat-header-cell *matHeaderCellDef>Type</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <mat-chip [class]=\"getRequestTypeClass(request.type)\">\n                        {{getRequestTypeLabel(request.type)}}\n                      </mat-chip>\n                    </td>\n                  </ng-container>\n\n                  <!-- Title Column -->\n                  <ng-container matColumnDef=\"title\">\n                    <th mat-header-cell *matHeaderCellDef>Request</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"request-title\">\n                        {{request.title || 'No Title'}}\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <!-- Current Step Column -->\n                  <ng-container matColumnDef=\"currentStep\">\n                    <th mat-header-cell *matHeaderCellDef>Current Step</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <span class=\"current-step\">{{getCurrentStepName(request)}}</span>\n                    </td>\n                  </ng-container>\n\n                  <!-- Actions Column -->\n                  <ng-container matColumnDef=\"actions\">\n                    <th mat-header-cell *matHeaderCellDef>Actions</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"action-buttons\">\n                        <button mat-icon-button [routerLink]=\"['/requests/details', request.id]\">\n                          <mat-icon>visibility</mat-icon>\n                        </button>\n                        <button mat-raised-button color=\"primary\" (click)=\"processRequest(request)\">\n                          <mat-icon>check</mat-icon>\n                          Process\n                        </button>\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <tr mat-header-row *matHeaderRowDef=\"hrColumns\"></tr>\n                  <tr mat-row *matRowDef=\"let row; columns: hrColumns;\"></tr>\n                </table>\n              </div>\n\n              <div *ngIf=\"pendingHRRequests.length === 0\" class=\"no-data\">\n                <mat-icon>assignment_turned_in</mat-icon>\n                <h3>No Pending Reviews</h3>\n                <p>All requests have been processed or there are no requests requiring HR review.</p>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Employee Management -->\n          <mat-tab label=\"Employee Management\">\n            <div class=\"tab-content\">\n              <div class=\"employee-stats\">\n                <div class=\"stat-section\">\n                  <h4>Department Breakdown</h4>\n                  <div class=\"department-list\">\n                    <div *ngFor=\"let dept of departments\" class=\"department-item\">\n                      <span class=\"dept-name\">{{dept.name}}</span>\n                      <span class=\"dept-count\">{{dept.count}} employees</span>\n                      <mat-progress-bar mode=\"determinate\" [value]=\"dept.percentage\"></mat-progress-bar>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"stat-section\">\n                  <h4>Recent Hires</h4>\n                  <div class=\"recent-hires\">\n                    <div *ngFor=\"let hire of recentHires\" class=\"hire-item\">\n                      <div class=\"hire-avatar\">{{getInitials(hire.name)}}</div>\n                      <div class=\"hire-info\">\n                        <strong>{{hire.name}}</strong>\n                        <small>{{hire.department}} - {{hire.startDate | date:'short'}}</small>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Analytics -->\n          <mat-tab label=\"Analytics\">\n            <div class=\"tab-content\">\n              <div class=\"analytics-grid\">\n                <div class=\"analytics-card\">\n                  <h4>Request Volume Trends</h4>\n                  <div class=\"chart-placeholder\">\n                    <mat-icon>trending_up</mat-icon>\n                    <p>Request volume chart would be displayed here</p>\n                  </div>\n                </div>\n\n                <div class=\"analytics-card\">\n                  <h4>Processing Efficiency</h4>\n                  <div class=\"efficiency-metrics\">\n                    <div class=\"efficiency-item\">\n                      <span class=\"label\">Average Processing Time:</span>\n                      <span class=\"value\">{{hrMetrics.averageProcessingTime}} hours</span>\n                    </div>\n                    <div class=\"efficiency-item\">\n                      <span class=\"label\">Requests Processed Today:</span>\n                      <span class=\"value\">{{dailyProcessed}}</span>\n                    </div>\n                    <div class=\"efficiency-item\">\n                      <span class=\"label\">Efficiency Score:</span>\n                      <span class=\"value\">{{efficiencyScore}}%</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n        </mat-tab-group>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .hr-dashboard {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    .dashboard-header {\n      margin-bottom: 2rem;\n    }\n\n    .dashboard-header h1 {\n      margin: 0;\n      color: #333;\n    }\n\n    .dashboard-header p {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .metric-card {\n      transition: transform 0.2s ease;\n    }\n\n    .metric-card:hover {\n      transform: translateY(-2px);\n    }\n\n    .metric-card.employees {\n      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\n      color: white;\n    }\n\n    .metric-card.active {\n      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);\n      color: white;\n    }\n\n    .metric-card.processed {\n      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);\n      color: white;\n    }\n\n    .metric-card.time {\n      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);\n      color: white;\n    }\n\n    .metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .metric-icon mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .metric-info h3 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: bold;\n    }\n\n    .metric-info p {\n      margin: 0.25rem 0 0 0;\n      font-size: 1rem;\n    }\n\n    .request-types-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .type-card {\n      background-color: #f8f9fa;\n      border-left: 4px solid;\n    }\n\n    .type-card.leave {\n      border-left-color: #2196f3;\n    }\n\n    .type-card.expense {\n      border-left-color: #ff9800;\n    }\n\n    .type-card.training {\n      border-left-color: #4caf50;\n    }\n\n    .type-metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .type-metric mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      color: #666;\n    }\n\n    .type-info h4 {\n      margin: 0;\n      font-size: 1.5rem;\n      color: #333;\n    }\n\n    .type-info p {\n      margin: 0.25rem 0 0 0;\n      color: #666;\n    }\n\n    .content-card {\n      margin-top: 1rem;\n    }\n\n    .tab-content {\n      padding: 1rem;\n    }\n\n    .tab-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .requests-table {\n      overflow-x: auto;\n    }\n\n    .requests-table table {\n      width: 100%;\n    }\n\n    .employee-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .employee-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .request-title {\n      max-width: 200px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .current-step {\n      background-color: #e3f2fd;\n      color: #1976d2;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n    }\n\n    .type-leave { background-color: #e3f2fd; color: #1976d2; }\n    .type-expense { background-color: #f3e5f5; color: #7b1fa2; }\n    .type-training { background-color: #e8f5e8; color: #2e7d32; }\n    .type-it { background-color: #fff3e0; color: #ef6c00; }\n    .type-profile { background-color: #fce4ec; color: #c2185b; }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #4caf50;\n    }\n\n    .employee-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 2rem;\n    }\n\n    .stat-section {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .stat-section h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .department-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .department-item {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .dept-name {\n      font-weight: 500;\n    }\n\n    .dept-count {\n      font-size: 0.8rem;\n      color: #666;\n    }\n\n    .recent-hires {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .hire-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .hire-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #2196f3;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      font-size: 0.9rem;\n    }\n\n    .hire-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .hire-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .analytics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 2rem;\n    }\n\n    .analytics-card {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .analytics-card h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .chart-placeholder {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .chart-placeholder mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    .efficiency-metrics {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .efficiency-item {\n      display: flex;\n      justify-content: space-between;\n      padding: 0.5rem;\n      background-color: white;\n      border-radius: 4px;\n    }\n\n    .efficiency-item .label {\n      font-weight: 500;\n    }\n\n    .efficiency-item .value {\n      color: #2196f3;\n      font-weight: bold;\n    }\n\n    @media (max-width: 768px) {\n      .metrics-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n\n      .request-types-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .tab-header {\n        flex-direction: column;\n        align-items: stretch;\n        gap: 1rem;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n\n      .employee-stats,\n      .analytics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    @media (max-width: 480px) {\n      .metrics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class HRDashboardComponent implements OnInit, OnDestroy {\n  private readonly destroy$ = new Subject<void>();\n\n  hrMetrics: HRMetrics = {\n    totalEmployees: 45,\n    activeRequests: 12,\n    processedThisMonth: 67,\n    averageProcessingTime: 6.5,\n    leaveRequests: 8,\n    expenseReports: 15,\n    trainingRequests: 4\n  };\n\n  pendingHRRequests: RequestDto[] = [];\n  hrColumns: string[] = ['employee', 'type', 'title', 'currentStep', 'actions'];\n\n  departments = [\n    { name: 'Engineering', count: 18, percentage: 40 },\n    { name: 'Sales', count: 12, percentage: 27 },\n    { name: 'Marketing', count: 8, percentage: 18 },\n    { name: 'HR', count: 4, percentage: 9 },\n    { name: 'Finance', count: 3, percentage: 6 }\n  ];\n\n  recentHires = [\n    { name: 'Alice Johnson', department: 'Engineering', startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },\n    { name: 'Bob Smith', department: 'Sales', startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000) },\n    { name: 'Carol Davis', department: 'Marketing', startDate: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000) }\n  ];\n\n  dailyProcessed = 5;\n  efficiencyScore = 92;\n\n  constructor(\n    private readonly requestService: RequestService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPendingHRRequests();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadPendingHRRequests(): void {\n    // Mock data for demonstration\n    this.pendingHRRequests = [\n      {\n        id: '1',\n        type: RequestType.Leave,\n        initiatorId: 'user1',\n        initiatorName: 'John Doe',\n        status: RequestStatus.Pending,\n        title: 'Annual Leave Request',\n        description: 'Family vacation',\n        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        isDeleted: false,\n        requestSteps: [\n          {\n            id: '1',\n            requestId: '1',\n            workflowStepId: 'step1',\n            status: 2,\n            workflowStepName: 'Manager Approval',\n            responsibleRole: 'Manager',\n            createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000),\n            isDeleted: false\n          },\n          {\n            id: '2',\n            requestId: '1',\n            workflowStepId: 'step2',\n            status: 1,\n            workflowStepName: 'HR Review',\n            responsibleRole: 'HR',\n            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),\n            isDeleted: false\n          }\n        ]\n      },\n      {\n        id: '2',\n        type: RequestType.Training,\n        initiatorId: 'user2',\n        initiatorName: 'Jane Smith',\n        status: RequestStatus.Pending,\n        title: 'AWS Certification Training',\n        description: 'Professional development',\n        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),\n        isDeleted: false,\n        requestSteps: [\n          {\n            id: '1',\n            requestId: '2',\n            workflowStepId: 'step1',\n            status: 2,\n            workflowStepName: 'Manager Approval',\n            responsibleRole: 'Manager',\n            createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000),\n            isDeleted: false\n          },\n          {\n            id: '2',\n            requestId: '2',\n            workflowStepId: 'step2',\n            status: 1,\n            workflowStepName: 'HR Review',\n            responsibleRole: 'HR',\n            createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),\n            isDeleted: false\n          }\n        ]\n      }\n    ];\n  }\n\n  getRequestTypeLabel(type: RequestType): string {\n    switch (type) {\n      case RequestType.Leave: return 'Leave';\n      case RequestType.Expense: return 'Expense';\n      case RequestType.Training: return 'Training';\n      case RequestType.ITSupport: return 'IT Support';\n      case RequestType.ProfileUpdate: return 'Profile';\n      default: return 'Unknown';\n    }\n  }\n\n  getRequestTypeClass(type: RequestType): string {\n    switch (type) {\n      case RequestType.Leave: return 'type-leave';\n      case RequestType.Expense: return 'type-expense';\n      case RequestType.Training: return 'type-training';\n      case RequestType.ITSupport: return 'type-it';\n      case RequestType.ProfileUpdate: return 'type-profile';\n      default: return '';\n    }\n  }\n\n  getCurrentStepName(request: RequestDto): string {\n    const currentStep = request.requestSteps?.find(step => step.status === 1); // Pending status\n    return currentStep?.workflowStepName || 'Unknown';\n  }\n\n  processRequest(request: RequestDto): void {\n    console.log('Process request:', request.id);\n    // In a real app, navigate to processing page or open dialog\n  }\n\n  getInitials(name: string): string {\n    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKoB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;;;;;AAC9C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA,EACV,GAAA,QAAA;AACjB,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;;AAAoC,IAAA,uBAAA,EAAQ,EAC/C;;;;AAFI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,aAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,WAAA,WAAA,OAAA,CAAA;;;;;AAOX,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,UAAA;AAEnC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAW;;;;;AAFD,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,oBAAA,WAAA,IAAA,CAAA;AACR,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,oBAAA,WAAA,IAAA,GAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA;AAEnC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM;;;;AADJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,SAAA,YAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;;;;;AAClD,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,QAAA,EAAA;AACV,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA,EAAO;;;;;AAAtC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,mBAAA,UAAA,CAAA;;;;;AAM7B,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA,EACT,GAAA,UAAA,EAAA,EAC+C,GAAA,UAAA;AAC7D,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;AAEjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0C,IAAA,qBAAA,SAAA,SAAA,qEAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,UAAA,CAAuB;IAAA,CAAA;AACxE,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACf,IAAA,iBAAA,GAAA,WAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAPoB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,GAAA,KAAA,WAAA,EAAA,CAAA;;;;;AAW9B,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AA1DJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,SAAA,EAAA;AAG7D,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA;;AAUxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA;;AAOxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA;;AAcxC,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA,EAAgD,IAAA,4CAAA,GAAA,GAAA,MAAA,EAAA;AAElD,IAAA,uBAAA,EAAQ;;;;AA1DS,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,OAAA,iBAAA;AAwDK,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,SAAA;AACa,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,SAAA;;;;;AAIrC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,UAAA;AAChD,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AAC9B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,gFAAA;AAA8E,IAAA,uBAAA,EAAI;;;;;AAYjF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,QAAA,EAAA;AACpC,IAAA,iBAAA,CAAA;AAAa,IAAA,uBAAA;AACrC,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyB,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AACjD,IAAA,oBAAA,GAAA,oBAAA,EAAA;AACF,IAAA,uBAAA;;;;AAH0B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,IAAA;AACC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,QAAA,OAAA,YAAA;AACY,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,QAAA,UAAA;;;;;AAQvC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,OAAA,EAAA;AAC7B,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AACnD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,QAAA;AACb,IAAA,iBAAA,CAAA;AAAa,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;;AAAuD,IAAA,uBAAA,EAAQ,EAClE;;;;;AAJmB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,QAAA,IAAA,CAAA;AAEf,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,IAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,QAAA,YAAA,OAAA,sBAAA,GAAA,GAAA,QAAA,WAAA,OAAA,GAAA,EAAA;;;AAgazB,IAAO,uBAAP,MAAO,sBAAoB;EAkCZ;EAjCF,WAAW,IAAI,QAAO;EAEvC,YAAuB;IACrB,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,uBAAuB;IACvB,eAAe;IACf,gBAAgB;IAChB,kBAAkB;;EAGpB,oBAAkC,CAAA;EAClC,YAAsB,CAAC,YAAY,QAAQ,SAAS,eAAe,SAAS;EAE5E,cAAc;IACZ,EAAE,MAAM,eAAe,OAAO,IAAI,YAAY,GAAE;IAChD,EAAE,MAAM,SAAS,OAAO,IAAI,YAAY,GAAE;IAC1C,EAAE,MAAM,aAAa,OAAO,GAAG,YAAY,GAAE;IAC7C,EAAE,MAAM,MAAM,OAAO,GAAG,YAAY,EAAC;IACrC,EAAE,MAAM,WAAW,OAAO,GAAG,YAAY,EAAC;;EAG5C,cAAc;IACZ,EAAE,MAAM,iBAAiB,YAAY,eAAe,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EAAC;IAC7G,EAAE,MAAM,aAAa,YAAY,SAAS,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAC;IACpG,EAAE,MAAM,eAAe,YAAY,aAAa,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,EAAC;;EAG5G,iBAAiB;EACjB,kBAAkB;EAElB,YACmB,gBAA8B;AAA9B,SAAA,iBAAA;EAChB;EAEH,WAAQ;AACN,SAAK,sBAAqB;EAC5B;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEA,wBAAqB;AAEnB,SAAK,oBAAoB;MACvB;QACE,IAAI;QACJ,MAAM,YAAY;QAClB,aAAa;QACb,eAAe;QACf,QAAQ,cAAc;QACtB,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;QACpD,WAAW;QACX,cAAc;UACZ;YACE,IAAI;YACJ,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,kBAAkB;YAClB,iBAAiB;YACjB,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;YACpD,WAAW;;UAEb;YACE,IAAI;YACJ,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,kBAAkB;YAClB,iBAAiB;YACjB,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;YACpD,WAAW;;;;MAIjB;QACE,IAAI;QACJ,MAAM,YAAY;QAClB,aAAa;QACb,eAAe;QACf,QAAQ,cAAc;QACtB,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;QACpD,WAAW;QACX,cAAc;UACZ;YACE,IAAI;YACJ,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,kBAAkB;YAClB,iBAAiB;YACjB,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;YACpD,WAAW;;UAEb;YACE,IAAI;YACJ,WAAW;YACX,gBAAgB;YAChB,QAAQ;YACR,kBAAkB;YAClB,iBAAiB;YACjB,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;YACpD,WAAW;;;;;EAKrB;EAEA,oBAAoB,MAAiB;AACnC,YAAQ,MAAM;MACZ,KAAK,YAAY;AAAO,eAAO;MAC/B,KAAK,YAAY;AAAS,eAAO;MACjC,KAAK,YAAY;AAAU,eAAO;MAClC,KAAK,YAAY;AAAW,eAAO;MACnC,KAAK,YAAY;AAAe,eAAO;MACvC;AAAS,eAAO;IAClB;EACF;EAEA,oBAAoB,MAAiB;AACnC,YAAQ,MAAM;MACZ,KAAK,YAAY;AAAO,eAAO;MAC/B,KAAK,YAAY;AAAS,eAAO;MACjC,KAAK,YAAY;AAAU,eAAO;MAClC,KAAK,YAAY;AAAW,eAAO;MACnC,KAAK,YAAY;AAAe,eAAO;MACvC;AAAS,eAAO;IAClB;EACF;EAEA,mBAAmB,SAAmB;AACpC,UAAM,cAAc,QAAQ,cAAc,KAAK,UAAQ,KAAK,WAAW,CAAC;AACxE,WAAO,aAAa,oBAAoB;EAC1C;EAEA,eAAe,SAAmB;AAChC,YAAQ,IAAI,oBAAoB,QAAQ,EAAE;EAE5C;EAEA,YAAY,MAAY;AACtB,WAAO,KAAK,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,YAAW;EACnE;;qCAxJW,uBAAoB,4BAAA,cAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,KAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,WAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,QAAA,GAAA,CAAA,GAAA,eAAA,WAAA,GAAA,CAAA,GAAA,eAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,aAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,SAAA,GAAA,CAAA,GAAA,aAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,WAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,aAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,aAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,UAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,CAAA,gBAAA,aAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAlnB7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,OAAA,CAAA,EACM,GAAA,IAAA;AACxB,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;AAChB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,2CAAA;AAAyC,MAAA,uBAAA,EAAI;AAIlD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,YAAA,CAAA,EACgB,GAAA,kBAAA,EACpB,GAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAW;AAE7B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAA4B,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAI,EAClB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAqC,IAAA,kBAAA,EACjB,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAW;AAEjC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAA4B,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAI,EAClB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAwC,IAAA,kBAAA,EACpB,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAW;AAE/B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAgC,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA,EAAI,EACvB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAmC,IAAA,kBAAA,EACf,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAW;AAE/B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAoC,MAAA,uBAAA;AACxC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA,EAAI,EACvB,EACF,EACW,EACV;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAgC,IAAA,YAAA,EAAA,EACI,IAAA,kBAAA,EACd,IAAA,OAAA,EAAA,EACS,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,MAAA,iBAAA,EAAA;AAA2B,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAI,EACjB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAoC,IAAA,kBAAA,EAChB,IAAA,OAAA,EAAA,EACS,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,MAAA,iBAAA,EAAA;AAA4B,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAI,EAClB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAqC,IAAA,kBAAA,EACjB,IAAA,OAAA,EAAA,EACS,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,MAAA,iBAAA,EAAA;AAA8B,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA,EAAI,EACpB,EACF,EACW,EACV;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA+B,IAAA,eAAA,EACd,IAAA,WAAA,EAAA,EAEkD,IAAA,OAAA,EAAA,EACpC,IAAA,OAAA,EAAA,EACC,IAAA,IAAA;AAClB,MAAA,iBAAA,IAAA,6BAAA;AAA2B,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAiE,IAAA,UAAA;AACrD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,qBAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,qBAAA,IAAA,sCAAA,IAAA,GAAA,OAAA,EAAA,EAAiE,IAAA,sCAAA,GAAA,GAAA,OAAA,EAAA;AAmEnE,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAqC,IAAA,OAAA,EAAA,EACV,IAAA,OAAA,EAAA,EACK,IAAA,OAAA,EAAA,EACA,IAAA,IAAA;AACpB,MAAA,iBAAA,KAAA,sBAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,yBAAA,KAAA,OAAA,EAAA;AACE,MAAA,qBAAA,KAAA,uCAAA,GAAA,GAAA,OAAA,EAAA;AAKF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,IAAA;AACpB,MAAA,iBAAA,KAAA,cAAA;AAAY,MAAA,uBAAA;AAChB,MAAA,yBAAA,KAAA,OAAA,EAAA;AACE,MAAA,qBAAA,KAAA,uCAAA,GAAA,GAAA,OAAA,EAAA;AAOF,MAAA,uBAAA,EAAM,EACF,EACF,EACF;AAIR,MAAA,yBAAA,KAAA,WAAA,EAAA,EAA2B,KAAA,OAAA,EAAA,EACA,KAAA,OAAA,EAAA,EACK,KAAA,OAAA,EAAA,EACE,KAAA,IAAA;AACtB,MAAA,iBAAA,KAAA,uBAAA;AAAqB,MAAA,uBAAA;AACzB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA+B,KAAA,UAAA;AACnB,MAAA,iBAAA,KAAA,aAAA;AAAW,MAAA,uBAAA;AACrB,MAAA,yBAAA,KAAA,GAAA;AAAG,MAAA,iBAAA,KAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAI,EAC/C;AAGR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,IAAA;AACtB,MAAA,iBAAA,KAAA,uBAAA;AAAqB,MAAA,uBAAA;AACzB,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAgC,KAAA,OAAA,EAAA,EACD,KAAA,QAAA,EAAA;AACP,MAAA,iBAAA,KAAA,0BAAA;AAAwB,MAAA,uBAAA;AAC5C,MAAA,yBAAA,KAAA,QAAA,EAAA;AAAoB,MAAA,iBAAA,GAAA;AAAyC,MAAA,uBAAA,EAAO;AAEtE,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA6B,KAAA,QAAA,EAAA;AACP,MAAA,iBAAA,KAAA,2BAAA;AAAyB,MAAA,uBAAA;AAC7C,MAAA,yBAAA,KAAA,QAAA,EAAA;AAAoB,MAAA,iBAAA,GAAA;AAAkB,MAAA,uBAAA,EAAO;AAE/C,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA6B,KAAA,QAAA,EAAA;AACP,MAAA,iBAAA,KAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrC,MAAA,yBAAA,KAAA,QAAA,EAAA;AAAoB,MAAA,iBAAA,GAAA;AAAoB,MAAA,uBAAA,EAAO,EAC3C,EACF,EACF,EACF,EACF,EACE,EACI,EACP;;;AA7OG,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,cAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,cAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,kBAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,UAAA,uBAAA,GAAA;AAeA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,aAAA;AAYA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,cAAA;AAYA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,UAAA,gBAAA;AAYD,MAAA,oBAAA,CAAA;AAAA,MAAA,iCAAA,SAAA,oBAAA,IAAA,kBAAA,QAAA,GAAA;AAUC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,kBAAA,SAAA,CAAA;AA8DA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,kBAAA,WAAA,CAAA;AAesB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,WAAA;AAWA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,WAAA;AA8BA,MAAA,oBAAA,EAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,UAAA,uBAAA,QAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,cAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,iBAAA,GAAA;;oBA/PtC,cAAY,SAAA,MAAA,UACZ,cAAY,YACZ,eAAa,SAAA,gBACb,iBAAe,WAAA,eACf,eAAa,SACb,gBAAc,UAAA,kBAAA,iBAAA,cAAA,YAAA,WAAA,eAAA,SAAA,cAAA,QACd,gBAAc,SACd,eAAa,QAAA,aACb,sBAAoB,cAAA,GAAA,QAAA,CAAA,6+OAAA,EAAA,CAAA;;;sEAqnBX,sBAAoB,CAAA;UAjoBhC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+PT,QAAA,CAAA,ojMAAA,EAAA,CAAA;;;;6EAoXU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,gFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}