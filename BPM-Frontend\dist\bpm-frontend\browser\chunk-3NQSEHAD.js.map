{"version": 3, "sources": ["src/app/core/services/reporting.service.ts", "src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.ts", "src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\n\nexport interface ReportFilter {\n  startDate?: Date;\n  endDate?: Date;\n  requestType?: string;\n  status?: string;\n  department?: string;\n  userId?: string;\n  workflowId?: string;\n}\n\nexport interface ReportData {\n  totalRequests: number;\n  pendingRequests: number;\n  approvedRequests: number;\n  rejectedRequests: number;\n  averageProcessingTime: number;\n  requestsByType: { [type: string]: number };\n  requestsByStatus: { [status: string]: number };\n  requestsByDepartment: { [department: string]: number };\n  processingTimeByType: { [type: string]: number };\n  approvalRateByManager: { [manager: string]: number };\n  monthlyTrends: {\n    month: string;\n    submitted: number;\n    approved: number;\n    rejected: number;\n  }[];\n}\n\nexport interface UserActivityReport {\n  userId: string;\n  userName: string;\n  department: string;\n  totalRequests: number;\n  pendingRequests: number;\n  approvedRequests: number;\n  rejectedRequests: number;\n  averageResponseTime: number;\n  lastActivity: Date;\n}\n\nexport interface WorkflowPerformanceReport {\n  workflowId: string;\n  workflowName: string;\n  totalRequests: number;\n  averageCompletionTime: number;\n  bottleneckSteps: {\n    stepName: string;\n    averageTime: number;\n    pendingCount: number;\n  }[];\n  completionRate: number;\n  userSatisfactionScore?: number;\n}\n\nexport interface SystemHealthReport {\n  totalUsers: number;\n  activeUsers: number;\n  totalWorkflows: number;\n  activeWorkflows: number;\n  systemUptime: number;\n  averageResponseTime: number;\n  errorRate: number;\n  storageUsage: {\n    used: number;\n    total: number;\n    percentage: number;\n  };\n  databaseHealth: {\n    connectionCount: number;\n    queryPerformance: number;\n    indexHealth: number;\n  };\n}\n\nexport interface ExportOptions {\n  format: 'pdf' | 'excel' | 'csv';\n  includeCharts?: boolean;\n  includeRawData?: boolean;\n  template?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ReportingService {\n  private readonly API_URL = `${environment.apiUrl}/api/reporting`;\n\n  constructor(private readonly http: HttpClient) {}\n\n  // General reporting\n  getSystemOverviewReport(filters?: ReportFilter): Observable<ReportData> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<ReportData>(`${this.API_URL}/overview`, { params });\n  }\n\n  getUserActivityReport(filters?: ReportFilter): Observable<UserActivityReport[]> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<UserActivityReport[]>(`${this.API_URL}/user-activity`, { params });\n  }\n\n  getWorkflowPerformanceReport(filters?: ReportFilter): Observable<WorkflowPerformanceReport[]> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<WorkflowPerformanceReport[]>(`${this.API_URL}/workflow-performance`, { params });\n  }\n\n  getSystemHealthReport(): Observable<SystemHealthReport> {\n    return this.http.get<SystemHealthReport>(`${this.API_URL}/system-health`);\n  }\n\n  // Specific analytics\n  getRequestTrends(period: 'daily' | 'weekly' | 'monthly' = 'monthly', filters?: ReportFilter): Observable<{\n    labels: string[];\n    datasets: {\n      label: string;\n      data: number[];\n      backgroundColor?: string;\n      borderColor?: string;\n    }[];\n  }> {\n    let params = this.buildFilterParams(filters);\n    params = params.set('period', period);\n    return this.http.get<any>(`${this.API_URL}/request-trends`, { params });\n  }\n\n  getApprovalRates(filters?: ReportFilter): Observable<{\n    overall: number;\n    byType: { [type: string]: number };\n    byDepartment: { [department: string]: number };\n    byManager: { [manager: string]: number };\n  }> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<any>(`${this.API_URL}/approval-rates`, { params });\n  }\n\n  getProcessingTimeAnalysis(filters?: ReportFilter): Observable<{\n    average: number;\n    median: number;\n    byType: { [type: string]: { average: number; median: number } };\n    byStep: { [step: string]: { average: number; median: number } };\n    distribution: {\n      range: string;\n      count: number;\n      percentage: number;\n    }[];\n  }> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<any>(`${this.API_URL}/processing-time`, { params });\n  }\n\n  getBottleneckAnalysis(filters?: ReportFilter): Observable<{\n    workflowId: string;\n    workflowName: string;\n    bottlenecks: {\n      stepName: string;\n      averageTime: number;\n      pendingCount: number;\n      impactScore: number;\n    }[];\n  }[]> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<any>(`${this.API_URL}/bottlenecks`, { params });\n  }\n\n  // Department-specific reports\n  getDepartmentReport(departmentId: string, filters?: ReportFilter): Observable<{\n    departmentName: string;\n    totalEmployees: number;\n    totalRequests: number;\n    averageProcessingTime: number;\n    topRequestTypes: { type: string; count: number }[];\n    employeeActivity: UserActivityReport[];\n    trends: any[];\n  }> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<any>(`${this.API_URL}/department/${departmentId}`, { params });\n  }\n\n  // Manager-specific reports\n  getManagerReport(managerId: string, filters?: ReportFilter): Observable<{\n    managerName: string;\n    teamSize: number;\n    pendingApprovals: number;\n    averageApprovalTime: number;\n    approvalRate: number;\n    teamActivity: UserActivityReport[];\n    workloadDistribution: any[];\n  }> {\n    let params = this.buildFilterParams(filters);\n    return this.http.get<any>(`${this.API_URL}/manager/${managerId}`, { params });\n  }\n\n  // Export functionality\n  exportReport(\n    reportType: 'overview' | 'user-activity' | 'workflow-performance' | 'system-health',\n    options: ExportOptions,\n    filters?: ReportFilter\n  ): Observable<Blob> {\n    let params = this.buildFilterParams(filters);\n    params = params.set('format', options.format);\n    \n    if (options.includeCharts !== undefined) {\n      params = params.set('includeCharts', options.includeCharts.toString());\n    }\n    \n    if (options.includeRawData !== undefined) {\n      params = params.set('includeRawData', options.includeRawData.toString());\n    }\n    \n    if (options.template) {\n      params = params.set('template', options.template);\n    }\n\n    return this.http.get(`${this.API_URL}/export/${reportType}`, {\n      params,\n      responseType: 'blob'\n    });\n  }\n\n  // Custom reports\n  createCustomReport(reportConfig: {\n    name: string;\n    description?: string;\n    filters: ReportFilter;\n    metrics: string[];\n    chartTypes: string[];\n    schedule?: {\n      frequency: 'daily' | 'weekly' | 'monthly';\n      recipients: string[];\n    };\n  }): Observable<{ reportId: string }> {\n    return this.http.post<{ reportId: string }>(`${this.API_URL}/custom`, reportConfig);\n  }\n\n  getCustomReports(): Observable<{\n    id: string;\n    name: string;\n    description?: string;\n    createdAt: Date;\n    lastRun?: Date;\n    isScheduled: boolean;\n  }[]> {\n    return this.http.get<any[]>(`${this.API_URL}/custom`);\n  }\n\n  runCustomReport(reportId: string): Observable<any> {\n    return this.http.post<any>(`${this.API_URL}/custom/${reportId}/run`, {});\n  }\n\n  deleteCustomReport(reportId: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/custom/${reportId}`);\n  }\n\n  // Scheduled reports\n  scheduleReport(reportId: string, schedule: {\n    frequency: 'daily' | 'weekly' | 'monthly';\n    recipients: string[];\n    format: 'pdf' | 'excel';\n  }): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/schedule/${reportId}`, schedule);\n  }\n\n  getScheduledReports(): Observable<{\n    id: string;\n    reportName: string;\n    frequency: string;\n    nextRun: Date;\n    recipients: string[];\n    isActive: boolean;\n  }[]> {\n    return this.http.get<any[]>(`${this.API_URL}/scheduled`);\n  }\n\n  // Utility methods\n  private buildFilterParams(filters?: ReportFilter): HttpParams {\n    let params = new HttpParams();\n    \n    if (filters) {\n      if (filters.startDate) {\n        params = params.set('startDate', filters.startDate.toISOString());\n      }\n      if (filters.endDate) {\n        params = params.set('endDate', filters.endDate.toISOString());\n      }\n      if (filters.requestType) {\n        params = params.set('requestType', filters.requestType);\n      }\n      if (filters.status) {\n        params = params.set('status', filters.status);\n      }\n      if (filters.department) {\n        params = params.set('department', filters.department);\n      }\n      if (filters.userId) {\n        params = params.set('userId', filters.userId);\n      }\n      if (filters.workflowId) {\n        params = params.set('workflowId', filters.workflowId);\n      }\n    }\n    \n    return params;\n  }\n}\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { ReportingService } from '../../../../core/services/reporting.service';\n\n@Component({\n  selector: 'app-reporting-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTabsModule,\n    MatTableModule,\n    MatProgressSpinnerModule\n  ],\n  templateUrl: './reporting-dashboard.component.html',\n  styleUrls: ['./reporting-dashboard.component.scss']\n})\nexport class ReportingDashboardComponent implements OnInit, OnDestroy {\n  private readonly destroy$ = new Subject<void>();\n\n  isLoading = false;\n  currentUser: any = null;\n\n  // Dashboard stats\n  systemStats = {\n    totalUsers: 0,\n    activeWorkflows: 0,\n    totalRequests: 0,\n    pendingApprovals: 0\n  };\n\n  constructor(\n    private readonly router: Router,\n    private readonly authService: AuthService,\n    private readonly reportingService: ReportingService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n    this.loadSystemStats();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        this.currentUser = user;\n      });\n  }\n\n  private loadSystemStats(): void {\n    this.isLoading = true;\n\n    // Mock data for now - replace with actual API calls\n    setTimeout(() => {\n      this.systemStats = {\n        totalUsers: 156,\n        activeWorkflows: 12,\n        totalRequests: 1247,\n        pendingApprovals: 23\n      };\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n}\n", "<div class=\"reporting-dashboard-container\">\n  <!-- Header Section -->\n  <div class=\"dashboard-header\">\n    <h1>Admin Dashboard</h1>\n    <p>Welcome back, {{currentUser?.userName}}! Here's your system overview.</p>\n  </div>\n\n  <!-- Loading Spinner -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <mat-spinner></mat-spinner>\n    <p>Loading dashboard data...</p>\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading\" class=\"dashboard-content\">\n\n    <!-- System Overview Cards -->\n    <div class=\"stats-grid\">\n      <mat-card class=\"stat-card\">\n        <mat-card-header>\n          <mat-icon mat-card-avatar class=\"users-icon\">people</mat-icon>\n          <mat-card-title>Total Users</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stat-number\">{{systemStats.totalUsers}}</div>\n          <div class=\"stat-label\">Registered Users</div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-header>\n          <mat-icon mat-card-avatar class=\"workflows-icon\">account_tree</mat-icon>\n          <mat-card-title>Active Workflows</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stat-number\">{{systemStats.activeWorkflows}}</div>\n          <div class=\"stat-label\">Running Processes</div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-header>\n          <mat-icon mat-card-avatar class=\"requests-icon\">assignment</mat-icon>\n          <mat-card-title>Total Requests</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stat-number\">{{systemStats.totalRequests}}</div>\n          <div class=\"stat-label\">All Time</div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-header>\n          <mat-icon mat-card-avatar class=\"pending-icon\">pending_actions</mat-icon>\n          <mat-card-title>Pending Approvals</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stat-number\">{{systemStats.pendingApprovals}}</div>\n          <div class=\"stat-label\">Awaiting Action</div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Admin Actions -->\n    <div class=\"admin-actions\">\n      <h2>Admin Actions</h2>\n      <div class=\"action-cards\">\n        <mat-card class=\"action-card\" (click)=\"navigateTo('/admin/users')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>manage_accounts</mat-icon>\n            <mat-card-title>User Management</mat-card-title>\n            <mat-card-subtitle>Manage users, roles, and permissions</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              Manage Users\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <mat-card class=\"action-card\" (click)=\"navigateTo('/admin/workflows')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>settings</mat-icon>\n            <mat-card-title>Workflow Designer</mat-card-title>\n            <mat-card-subtitle>Create and configure workflows</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              Design Workflows\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <mat-card class=\"action-card\" (click)=\"navigateTo('/admin/reports')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>analytics</mat-icon>\n            <mat-card-title>System Reports</mat-card-title>\n            <mat-card-subtitle>Generate detailed system reports</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              View Reports\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <mat-card class=\"action-card\" (click)=\"navigateTo('/admin/settings')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>tune</mat-icon>\n            <mat-card-title>System Settings</mat-card-title>\n            <mat-card-subtitle>Configure system preferences</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              Settings\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n\n    <!-- Quick Reports Section -->\n    <div class=\"quick-reports\">\n      <h2>Quick Reports</h2>\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Generate Reports</mat-card-title>\n          <mat-card-subtitle>Export system data and analytics</mat-card-subtitle>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"report-buttons\">\n            <button mat-raised-button color=\"primary\">\n              <mat-icon>picture_as_pdf</mat-icon>\n              Export to PDF\n            </button>\n            <button mat-raised-button color=\"accent\">\n              <mat-icon>table_chart</mat-icon>\n              Export to Excel\n            </button>\n            <button mat-raised-button>\n              <mat-icon>bar_chart</mat-icon>\n              Analytics Report\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FM,IAAO,mBAAP,MAAO,kBAAgB;EAGE;EAFZ,UAAU,GAAG,YAAY,MAAM;EAEhD,YAA6B,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;EAGhD,wBAAwB,SAAsB;AAC5C,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAgB,GAAG,KAAK,OAAO,aAAa,EAAE,OAAM,CAAE;EACzE;EAEA,sBAAsB,SAAsB;AAC1C,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAA0B,GAAG,KAAK,OAAO,kBAAkB,EAAE,OAAM,CAAE;EACxF;EAEA,6BAA6B,SAAsB;AACjD,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAiC,GAAG,KAAK,OAAO,yBAAyB,EAAE,OAAM,CAAE;EACtG;EAEA,wBAAqB;AACnB,WAAO,KAAK,KAAK,IAAwB,GAAG,KAAK,OAAO,gBAAgB;EAC1E;;EAGA,iBAAiB,SAAyC,WAAW,SAAsB;AASzF,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,aAAS,OAAO,IAAI,UAAU,MAAM;AACpC,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,mBAAmB,EAAE,OAAM,CAAE;EACxE;EAEA,iBAAiB,SAAsB;AAMrC,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,mBAAmB,EAAE,OAAM,CAAE;EACxE;EAEA,0BAA0B,SAAsB;AAW9C,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,oBAAoB,EAAE,OAAM,CAAE;EACzE;EAEA,sBAAsB,SAAsB;AAU1C,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,gBAAgB,EAAE,OAAM,CAAE;EACrE;;EAGA,oBAAoB,cAAsB,SAAsB;AAS9D,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,eAAe,YAAY,IAAI,EAAE,OAAM,CAAE;EACpF;;EAGA,iBAAiB,WAAmB,SAAsB;AASxD,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,YAAY,SAAS,IAAI,EAAE,OAAM,CAAE;EAC9E;;EAGA,aACE,YACA,SACA,SAAsB;AAEtB,QAAI,SAAS,KAAK,kBAAkB,OAAO;AAC3C,aAAS,OAAO,IAAI,UAAU,QAAQ,MAAM;AAE5C,QAAI,QAAQ,kBAAkB,QAAW;AACvC,eAAS,OAAO,IAAI,iBAAiB,QAAQ,cAAc,SAAQ,CAAE;IACvE;AAEA,QAAI,QAAQ,mBAAmB,QAAW;AACxC,eAAS,OAAO,IAAI,kBAAkB,QAAQ,eAAe,SAAQ,CAAE;IACzE;AAEA,QAAI,QAAQ,UAAU;AACpB,eAAS,OAAO,IAAI,YAAY,QAAQ,QAAQ;IAClD;AAEA,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,WAAW,UAAU,IAAI;MAC3D;MACA,cAAc;KACf;EACH;;EAGA,mBAAmB,cAUlB;AACC,WAAO,KAAK,KAAK,KAA2B,GAAG,KAAK,OAAO,WAAW,YAAY;EACpF;EAEA,mBAAgB;AAQd,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,SAAS;EACtD;EAEA,gBAAgB,UAAgB;AAC9B,WAAO,KAAK,KAAK,KAAU,GAAG,KAAK,OAAO,WAAW,QAAQ,QAAQ,CAAA,CAAE;EACzE;EAEA,mBAAmB,UAAgB;AACjC,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,WAAW,QAAQ,EAAE;EACpE;;EAGA,eAAe,UAAkB,UAIhC;AACC,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,aAAa,QAAQ,IAAI,QAAQ;EAC9E;EAEA,sBAAmB;AAQjB,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,YAAY;EACzD;;EAGQ,kBAAkB,SAAsB;AAC9C,QAAI,SAAS,IAAI,WAAU;AAE3B,QAAI,SAAS;AACX,UAAI,QAAQ,WAAW;AACrB,iBAAS,OAAO,IAAI,aAAa,QAAQ,UAAU,YAAW,CAAE;MAClE;AACA,UAAI,QAAQ,SAAS;AACnB,iBAAS,OAAO,IAAI,WAAW,QAAQ,QAAQ,YAAW,CAAE;MAC9D;AACA,UAAI,QAAQ,aAAa;AACvB,iBAAS,OAAO,IAAI,eAAe,QAAQ,WAAW;MACxD;AACA,UAAI,QAAQ,QAAQ;AAClB,iBAAS,OAAO,IAAI,UAAU,QAAQ,MAAM;MAC9C;AACA,UAAI,QAAQ,YAAY;AACtB,iBAAS,OAAO,IAAI,cAAc,QAAQ,UAAU;MACtD;AACA,UAAI,QAAQ,QAAQ;AAClB,iBAAS,OAAO,IAAI,UAAU,QAAQ,MAAM;MAC9C;AACA,UAAI,QAAQ,YAAY;AACtB,iBAAS,OAAO,IAAI,cAAc,QAAQ,UAAU;MACtD;IACF;AAEA,WAAO;EACT;;qCAzNW,mBAAgB,mBAAA,UAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;;;;;AEjFC,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA,EAAI;;;;;;AAIlC,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAkD,GAAA,OAAA,CAAA,EAGxB,GAAA,YAAA,CAAA,EACM,GAAA,iBAAA,EACT,GAAA,YAAA,CAAA;AAC8B,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AACnD,IAAA,yBAAA,GAAA,gBAAA;AAAgB,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA,EAAiB;AAE9C,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA;AACS,IAAA,iBAAA,EAAA;AAA0B,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA,EAAM,EAC7B;AAGrB,IAAA,yBAAA,IAAA,YAAA,CAAA,EAA4B,IAAA,iBAAA,EACT,IAAA,YAAA,EAAA;AACkC,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAC7D,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA,EAAiB;AAEnD,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA;AACS,IAAA,iBAAA,EAAA;AAA+B,IAAA,uBAAA;AACxD,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA,EAAM,EAC9B;AAGrB,IAAA,yBAAA,IAAA,YAAA,CAAA,EAA4B,IAAA,iBAAA,EACT,IAAA,YAAA,EAAA;AACiC,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA,EAAiB;AAEjD,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA;AACS,IAAA,iBAAA,EAAA;AAA6B,IAAA,uBAAA;AACtD,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA,EAAM,EACrB;AAGrB,IAAA,yBAAA,IAAA,YAAA,CAAA,EAA4B,IAAA,iBAAA,EACT,IAAA,YAAA,EAAA;AACgC,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AAC9D,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA,EAAiB;AAEpD,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA;AACS,IAAA,iBAAA,EAAA;AAAgC,IAAA,uBAAA;AACzD,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA,EAAM,EAC5B,EACV;AAIb,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,IAAA;AACrB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,YAAA,EAAA;AACM,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,cAAc,CAAC;IAAA,CAAA;AAC/D,IAAA,yBAAA,IAAA,iBAAA,EAAiB,IAAA,YAAA,EAAA;AACW,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AACzC,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AAC/B,IAAA,yBAAA,IAAA,mBAAA;AAAmB,IAAA,iBAAA,IAAA,sCAAA;AAAoC,IAAA,uBAAA,EAAoB;AAE7E,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;AAGrB,IAAA,yBAAA,IAAA,YAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,kBAAkB,CAAC;IAAA,CAAA;AACnE,IAAA,yBAAA,IAAA,iBAAA,EAAiB,IAAA,YAAA,EAAA;AACW,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AAClC,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AACjC,IAAA,yBAAA,IAAA,mBAAA;AAAmB,IAAA,iBAAA,IAAA,gCAAA;AAA8B,IAAA,uBAAA,EAAoB;AAEvE,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,oBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;AAGrB,IAAA,yBAAA,IAAA,YAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,gBAAgB,CAAC;IAAA,CAAA;AACjE,IAAA,yBAAA,IAAA,iBAAA,EAAiB,IAAA,YAAA,EAAA;AACW,IAAA,iBAAA,IAAA,WAAA;AAAS,IAAA,uBAAA;AACnC,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,mBAAA;AAAmB,IAAA,iBAAA,IAAA,kCAAA;AAAgC,IAAA,uBAAA,EAAoB;AAEzE,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;AAGrB,IAAA,yBAAA,IAAA,YAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,iBAAiB,CAAC;IAAA,CAAA;AAClE,IAAA,yBAAA,IAAA,iBAAA,EAAiB,IAAA,YAAA,EAAA;AACW,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,gBAAA;AAAgB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AAC/B,IAAA,yBAAA,IAAA,mBAAA;AAAmB,IAAA,iBAAA,IAAA,8BAAA;AAA4B,IAAA,uBAAA,EAAoB;AAErE,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,KAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,KAAA,YAAA;AACF,IAAA,uBAAA,EAAS,EACQ,EACV,EACP;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA2B,KAAA,IAAA;AACrB,IAAA,iBAAA,KAAA,eAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,KAAA,UAAA,EAAU,KAAA,iBAAA,EACS,KAAA,gBAAA;AACC,IAAA,iBAAA,KAAA,kBAAA;AAAgB,IAAA,uBAAA;AAChC,IAAA,yBAAA,KAAA,mBAAA;AAAmB,IAAA,iBAAA,KAAA,kCAAA;AAAgC,IAAA,uBAAA,EAAoB;AAEzE,IAAA,yBAAA,KAAA,kBAAA,EAAkB,KAAA,OAAA,EAAA,EACY,KAAA,UAAA,EAAA,EACgB,KAAA,UAAA;AAC9B,IAAA,iBAAA,KAAA,gBAAA;AAAc,IAAA,uBAAA;AACxB,IAAA,iBAAA,KAAA,iBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,KAAA,UAAA,EAAA,EAAyC,KAAA,UAAA;AAC7B,IAAA,iBAAA,KAAA,aAAA;AAAW,IAAA,uBAAA;AACrB,IAAA,iBAAA,KAAA,mBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,KAAA,UAAA,EAAA,EAA0B,KAAA,UAAA;AACd,IAAA,iBAAA,KAAA,WAAA;AAAS,IAAA,uBAAA;AACnB,IAAA,iBAAA,KAAA,oBAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACW,EACV,EACP;;;;AA9HyB,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,UAAA;AAWA,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,eAAA;AAWA,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,aAAA;AAWA,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,gBAAA;;;AD3B7B,IAAO,8BAAP,MAAO,6BAA2B;EAenB;EACA;EACA;EAhBF,WAAW,IAAI,QAAO;EAEvC,YAAY;EACZ,cAAmB;;EAGnB,cAAc;IACZ,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,kBAAkB;;EAGpB,YACmB,QACA,aACA,kBAAkC;AAFlC,SAAA,SAAA;AACA,SAAA,cAAA;AACA,SAAA,mBAAA;EAChB;EAEH,WAAQ;AACN,SAAK,aAAY;AACjB,SAAK,gBAAe;EACtB;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEQ,eAAY;AAClB,SAAK,YAAY,aACd,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,UAAO;AAChB,WAAK,cAAc;IACrB,CAAC;EACL;EAEQ,kBAAe;AACrB,SAAK,YAAY;AAGjB,eAAW,MAAK;AACd,WAAK,cAAc;QACjB,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,kBAAkB;;AAEpB,WAAK,YAAY;IACnB,GAAG,GAAI;EACT;EAEA,WAAW,OAAa;AACtB,SAAK,OAAO,SAAS,CAAC,KAAK,CAAC;EAC9B;;qCAvDW,8BAA2B,4BAAA,MAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,gBAAA,CAAA;EAAA;yEAA3B,8BAA2B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,+BAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,eAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,cAAA,IAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,QAAA,GAAA,CAAA,qBAAA,EAAA,CAAA,GAAA,UAAA,SAAA,qCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AC9BxC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2C,GAAA,OAAA,CAAA,EAEX,GAAA,IAAA;AACxB,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA;AACnB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,CAAA;AAAqE,MAAA,uBAAA,EAAI;AAI9E,MAAA,qBAAA,GAAA,4CAAA,GAAA,GAAA,OAAA,CAAA,EAAiD,GAAA,4CAAA,KAAA,GAAA,OAAA,CAAA;AAiJnD,MAAA,uBAAA;;;AArJO,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,kBAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,UAAA,gCAAA;AAIC,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;;;IDIJ;IAAY;IACZ;IACA;IAAa;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACb;IAAe;IACf;IAAa;IACb;IACA;IACA;IAAwB;EAAA,GAAA,QAAA,CAAA,+7HAAA,EAAA,CAAA;;;sEAKf,6BAA2B,CAAA;UAhBvC;uBACW,2BAAyB,YACvB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,g5FAAA,EAAA,CAAA;;;;6EAIU,6BAA2B,EAAA,WAAA,+BAAA,UAAA,8FAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}