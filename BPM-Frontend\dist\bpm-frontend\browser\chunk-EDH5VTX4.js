import {
  environment
} from "./chunk-AKJJBQK4.js";
import {
  Router
} from "./chunk-ESNMJU6B.js";
import {
  HttpClient
} from "./chunk-RCHKY2RO.js";
import {
  BehaviorSubject,
  Injectable,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-JTRMQXMJ.js";

// src/app/core/services/auth.service.ts
var AuthService = class _AuthService {
  http;
  router;
  API_URL = `${environment.apiUrl}/api/Authentication`;
  TOKEN_KEY = "bpm_token";
  USER_KEY = "bpm_user";
  currentUserSubject = new BehaviorSubject(null);
  isAuthenticatedSubject = new BehaviorSubject(false);
  currentUser$ = this.currentUserSubject.asObservable();
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  redirectUrl = null;
  constructor(http, router) {
    this.http = http;
    this.router = router;
    this.initializeAuth();
  }
  initializeAuth() {
    const token = this.getToken();
    const user = this.getStoredUser();
    if (token && user && !this.isTokenExpired(token)) {
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    } else {
      this.clearAuthData();
    }
  }
  login(credentials) {
    return this.http.post(`${this.API_URL}/login`, credentials).pipe(tap((response) => {
      if (response.IsAuthSuccessful && response.Token && response.User) {
        this.setToken(response.Token);
        this.setUser(response.User);
        this.currentUserSubject.next(response.User);
        this.isAuthenticatedSubject.next(true);
      }
    }));
  }
  register(userData) {
    return this.http.post(`${this.API_URL}/register`, userData);
  }
  changePassword(passwordData) {
    return this.http.post(`${this.API_URL}/change-password`, passwordData);
  }
  logout() {
    this.clearAuthData();
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(["/auth/login"]);
  }
  getToken() {
    try {
      return localStorage.getItem(this.TOKEN_KEY);
    } catch {
      return null;
    }
  }
  getCurrentUser() {
    return this.currentUserSubject.value;
  }
  hasRole(role) {
    const user = this.getCurrentUser();
    const userRoles = user?.roles || user?.Roles || [];
    return userRoles.includes(role);
  }
  hasAnyRole(roles) {
    const user = this.getCurrentUser();
    const userRoles = user?.roles || user?.Roles || [];
    if (userRoles.length === 0)
      return false;
    return roles.some((role) => userRoles.includes(role));
  }
  // Get the appropriate dashboard route based on user roles
  getDashboardRoute() {
    const user = this.getCurrentUser();
    const userRoles = user?.roles || user?.Roles || [];
    console.log("User object:", user);
    console.log("User roles detected:", userRoles);
    if (userRoles.length === 0) {
      console.log("No roles found, defaulting to employee dashboard");
      return "/dashboard/employee";
    }
    if (userRoles.includes("Admin")) {
      console.log("Admin role detected, redirecting to reporting dashboard");
      return "/dashboard/reporting";
    }
    if (userRoles.includes("HR")) {
      console.log("HR role detected, redirecting to HR dashboard");
      return "/dashboard/hr";
    }
    if (userRoles.includes("Manager")) {
      console.log("Manager role detected, redirecting to manager dashboard");
      return "/dashboard/manager";
    }
    console.log("No specific role matched, defaulting to employee dashboard");
    return "/dashboard/employee";
  }
  isAuthenticated() {
    return this.isAuthenticatedSubject.value;
  }
  refreshUserProfile() {
    return this.http.get(`${this.API_URL}/profile`).pipe(tap((user) => {
      this.setUser(user);
      this.currentUserSubject.next(user);
    }));
  }
  setToken(token) {
    try {
      localStorage.setItem(this.TOKEN_KEY, token);
    } catch (error) {
      console.warn("Could not save token to localStorage:", error);
    }
  }
  setUser(user) {
    try {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    } catch (error) {
      console.warn("Could not save user to localStorage:", error);
    }
  }
  getStoredUser() {
    try {
      const userStr = localStorage.getItem(this.USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  }
  clearAuthData() {
    try {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    } catch (error) {
      console.warn("Could not clear auth data from localStorage:", error);
    }
  }
  isTokenExpired(token) {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Math.floor(Date.now() / 1e3);
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }
  static \u0275fac = function AuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: Router }], null);
})();

export {
  AuthService
};
//# sourceMappingURL=chunk-EDH5VTX4.js.map
