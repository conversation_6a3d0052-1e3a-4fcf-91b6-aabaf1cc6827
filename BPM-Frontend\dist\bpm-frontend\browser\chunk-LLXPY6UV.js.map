{"version": 3, "sources": ["src/app/features/dashboard/components/dashboard/dashboard.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { UserDto } from '../../../../core/models/auth.models';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <div class=\"welcome-section\">\n        <h1>Welcome back, {{currentUser?.firstName}}!</h1>\n        <p>Here's what's happening with your business processes today.</p>\n      </div>\n\n      <div class=\"dashboard-grid\">\n        <!-- Employee Dashboard Card -->\n        <mat-card class=\"dashboard-card\" (click)=\"navigateTo('/dashboard/employee')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>person</mat-icon>\n            <mat-card-title>My Dashboard</mat-card-title>\n            <mat-card-subtitle>View your requests and tasks</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"card-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{employeeStats.pendingRequests}}</span>\n                <span class=\"stat-label\">Pending Requests</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{employeeStats.completedRequests}}</span>\n                <span class=\"stat-label\">Completed</span>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              View Details\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <!-- Manager Dashboard Card -->\n        <mat-card \n          class=\"dashboard-card\" \n          *ngIf=\"hasManagerAccess\"\n          (click)=\"navigateTo('/dashboard/manager')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>supervisor_account</mat-icon>\n            <mat-card-title>Manager Dashboard</mat-card-title>\n            <mat-card-subtitle>Approve and manage team requests</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"card-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{managerStats.pendingApprovals}}</span>\n                <span class=\"stat-label\">Pending Approvals</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{managerStats.teamRequests}}</span>\n                <span class=\"stat-label\">Team Requests</span>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              View Details\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <!-- HR Dashboard Card -->\n        <mat-card \n          class=\"dashboard-card\" \n          *ngIf=\"hasHRAccess\"\n          (click)=\"navigateTo('/dashboard/hr')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>people</mat-icon>\n            <mat-card-title>HR Dashboard</mat-card-title>\n            <mat-card-subtitle>Process and archive requests</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"card-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{hrStats.toProcess}}</span>\n                <span class=\"stat-label\">To Process</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{hrStats.processed}}</span>\n                <span class=\"stat-label\">Processed Today</span>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              View Details\n            </button>\n          </mat-card-actions>\n        </mat-card>\n\n        <!-- Reports Dashboard Card -->\n        <mat-card \n          class=\"dashboard-card\" \n          *ngIf=\"hasReportAccess\"\n          (click)=\"navigateTo('/dashboard/reports')\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>analytics</mat-icon>\n            <mat-card-title>Reports & Analytics</mat-card-title>\n            <mat-card-subtitle>View insights and statistics</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"card-stats\">\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{reportStats.totalRequests}}</span>\n                <span class=\"stat-label\">Total Requests</span>\n              </div>\n              <div class=\"stat-item\">\n                <span class=\"stat-number\">{{reportStats.avgProcessingTime}}</span>\n                <span class=\"stat-label\">Avg. Processing Time</span>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-button color=\"primary\">\n              <mat-icon>arrow_forward</mat-icon>\n              View Details\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n\n      <!-- Quick Actions Section -->\n      <div class=\"quick-actions-section\">\n        <h2>Quick Actions</h2>\n        <div class=\"actions-grid\">\n          <button mat-raised-button color=\"primary\" routerLink=\"/requests/new\">\n            <mat-icon>add</mat-icon>\n            New Request\n          </button>\n          <button mat-raised-button color=\"accent\" routerLink=\"/requests\">\n            <mat-icon>list</mat-icon>\n            My Requests\n          </button>\n          <button mat-raised-button routerLink=\"/workflows\" *ngIf=\"hasAdminAccess\">\n            <mat-icon>account_tree</mat-icon>\n            Manage Workflows\n          </button>\n          <button mat-raised-button routerLink=\"/admin/users\" *ngIf=\"hasAdminAccess\">\n            <mat-icon>group</mat-icon>\n            Manage Users\n          </button>\n        </div>\n      </div>\n\n      <!-- Recent Activity Section -->\n      <div class=\"recent-activity-section\">\n        <mat-card>\n          <mat-card-header>\n            <mat-card-title>Recent Activity</mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"activity-list\" *ngIf=\"recentActivities.length > 0; else noActivity\">\n              <div class=\"activity-item\" *ngFor=\"let activity of recentActivities\">\n                <mat-icon [color]=\"getActivityColor(activity.type)\">{{getActivityIcon(activity.type)}}</mat-icon>\n                <div class=\"activity-content\">\n                  <div class=\"activity-title\">{{activity.title}}</div>\n                  <div class=\"activity-description\">{{activity.description}}</div>\n                  <div class=\"activity-time\">{{formatTime(activity.timestamp)}}</div>\n                </div>\n              </div>\n            </div>\n            <ng-template #noActivity>\n              <div class=\"no-activity\">\n                <mat-icon>inbox</mat-icon>\n                <p>No recent activity</p>\n              </div>\n            </ng-template>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  currentUser: UserDto | null = null;\n  hasManagerAccess = false;\n  hasHRAccess = false;\n  hasAdminAccess = false;\n  hasReportAccess = false;\n\n  employeeStats = {\n    pendingRequests: 0,\n    completedRequests: 0\n  };\n\n  managerStats = {\n    pendingApprovals: 0,\n    teamRequests: 0\n  };\n\n  hrStats = {\n    toProcess: 0,\n    processed: 0\n  };\n\n  reportStats = {\n    totalRequests: 0,\n    avgProcessingTime: '0h'\n  };\n\n  recentActivities: any[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n    this.loadDashboardStats();\n    this.loadRecentActivity();\n\n    // Automatically redirect to role-specific dashboard\n    this.redirectToRoleDashboard();\n  }\n\n  private redirectToRoleDashboard(): void {\n    const dashboardRoute = this.authService.getDashboardRoute();\n    // Only redirect if we're not already on the specific dashboard\n    if (this.router.url === '/dashboard' || this.router.url === '/dashboard/') {\n      this.router.navigate([dashboardRoute]);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(user => {\n        this.currentUser = user;\n        if (user) {\n          this.hasManagerAccess = this.authService.hasAnyRole(['Manager', 'Admin']);\n          this.hasHRAccess = this.authService.hasAnyRole(['HR', 'Admin']);\n          this.hasAdminAccess = this.authService.hasRole('Admin');\n          this.hasReportAccess = this.authService.hasAnyRole(['Manager', 'HR', 'Admin']);\n        }\n      });\n  }\n\n  private loadDashboardStats(): void {\n    // TODO: Implement actual API calls to load statistics\n    // For now, using mock data\n    this.employeeStats = {\n      pendingRequests: 3,\n      completedRequests: 12\n    };\n\n    this.managerStats = {\n      pendingApprovals: 5,\n      teamRequests: 18\n    };\n\n    this.hrStats = {\n      toProcess: 8,\n      processed: 15\n    };\n\n    this.reportStats = {\n      totalRequests: 156,\n      avgProcessingTime: '2.5h'\n    };\n  }\n\n  private loadRecentActivity(): void {\n    // TODO: Implement actual API call to load recent activities\n    // For now, using mock data\n    this.recentActivities = [\n      {\n        type: 'request_submitted',\n        title: 'Leave Request Submitted',\n        description: 'Your annual leave request has been submitted for approval',\n        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago\n      },\n      {\n        type: 'request_approved',\n        title: 'Expense Report Approved',\n        description: 'Your expense report #ER-2024-001 has been approved',\n        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago\n      },\n      {\n        type: 'workflow_updated',\n        title: 'Workflow Updated',\n        description: 'The IT Support workflow has been updated',\n        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago\n      }\n    ];\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'request_submitted': 'send',\n      'request_approved': 'check_circle',\n      'request_rejected': 'cancel',\n      'workflow_updated': 'update',\n      'user_assigned': 'person_add'\n    };\n    return iconMap[type] || 'info';\n  }\n\n  getActivityColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'request_submitted': 'primary',\n      'request_approved': 'primary',\n      'request_rejected': 'warn',\n      'workflow_updated': 'accent',\n      'user_assigned': 'primary'\n    };\n    return colorMap[type] || '';\n  }\n\n  formatTime(timestamp: Date): string {\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 60) {\n      return `${diffInMinutes} minutes ago`;\n    } else if (diffInMinutes < 1440) {\n      return `${Math.floor(diffInMinutes / 60)} hours ago`;\n    } else {\n      return `${Math.floor(diffInMinutes / 1440)} days ago`;\n    }\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDQ,IAAA,yBAAA,GAAA,YAAA,CAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,oBAAoB,CAAC;IAAA,CAAA;AACzC,IAAA,yBAAA,GAAA,iBAAA,EAAiB,GAAA,YAAA,CAAA;AACW,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,gBAAA;AAAgB,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,kCAAA;AAAgC,IAAA,uBAAA,EAAoB;AAEzE,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACQ,IAAA,OAAA,CAAA,EACC,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAAiC,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA,EAAO;AAEnD,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAA6B,IAAA,uBAAA;AACvD,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA,EAAO,EACzC,EACF;AAER,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;;;;AAda,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,gBAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,YAAA;;;;;;AAclC,IAAA,yBAAA,GAAA,YAAA,CAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,eAAe,CAAC;IAAA,CAAA;AACpC,IAAA,yBAAA,GAAA,iBAAA,EAAiB,GAAA,YAAA,CAAA;AACW,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AAChC,IAAA,yBAAA,GAAA,gBAAA;AAAgB,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,8BAAA;AAA4B,IAAA,uBAAA,EAAoB;AAErE,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACQ,IAAA,OAAA,CAAA,EACC,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAAqB,IAAA,uBAAA;AAC/C,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA,EAAO;AAE5C,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAAqB,IAAA,uBAAA;AAC/C,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA,EAAO,EAC3C,EACF;AAER,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;;;;AAda,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,SAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,SAAA;;;;;;AAclC,IAAA,yBAAA,GAAA,YAAA,CAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAW,oBAAoB,CAAC;IAAA,CAAA;AACzC,IAAA,yBAAA,GAAA,iBAAA,EAAiB,GAAA,YAAA,CAAA;AACW,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;AACnC,IAAA,yBAAA,GAAA,gBAAA;AAAgB,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA;AACnC,IAAA,yBAAA,GAAA,mBAAA;AAAmB,IAAA,iBAAA,GAAA,8BAAA;AAA4B,IAAA,uBAAA,EAAoB;AAErE,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACQ,IAAA,OAAA,CAAA,EACC,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAA6B,IAAA,uBAAA;AACvD,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA,EAAO;AAEhD,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,QAAA,CAAA;AACK,IAAA,iBAAA,EAAA;AAAiC,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,IAAA,iBAAA,IAAA,sBAAA;AAAoB,IAAA,uBAAA,EAAO,EAChD,EACF;AAER,IAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvB,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA,EAAS,EACQ;;;;AAda,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,aAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,iBAAA;;;;;AA0BhC,IAAA,yBAAA,GAAA,UAAA,EAAA,EAAyE,GAAA,UAAA;AAC7D,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;AACtB,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA,EAA2E,GAAA,UAAA;AAC/D,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACf,IAAA,iBAAA,GAAA,gBAAA;AACF,IAAA,uBAAA;;;;;AAYI,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,YAAA,EAAA;AACf,IAAA,iBAAA,CAAA;AAAkC,IAAA,uBAAA;AACtF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAkC,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,iBAAA,CAAA;AAAkC,IAAA,uBAAA,EAAM,EAC/D;;;;;AALI,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,iBAAA,YAAA,IAAA,CAAA;AAA0C,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,YAAA,IAAA,CAAA;AAEtB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,KAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,WAAA;AACP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,YAAA,SAAA,CAAA;;;;;AANjC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0CAAA,IAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;AARkD,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,gBAAA;;;;;AAUhD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,UAAA;AACb,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACf,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA,EAAI;;;AAUnC,IAAO,qBAAP,MAAO,oBAAkB;EAgCnB;EACA;EAhCF,WAAW,IAAI,QAAO;EAE9B,cAA8B;EAC9B,mBAAmB;EACnB,cAAc;EACd,iBAAiB;EACjB,kBAAkB;EAElB,gBAAgB;IACd,iBAAiB;IACjB,mBAAmB;;EAGrB,eAAe;IACb,kBAAkB;IAClB,cAAc;;EAGhB,UAAU;IACR,WAAW;IACX,WAAW;;EAGb,cAAc;IACZ,eAAe;IACf,mBAAmB;;EAGrB,mBAA0B,CAAA;EAE1B,YACU,aACA,QAAc;AADd,SAAA,cAAA;AACA,SAAA,SAAA;EACP;EAEH,WAAQ;AACN,SAAK,aAAY;AACjB,SAAK,mBAAkB;AACvB,SAAK,mBAAkB;AAGvB,SAAK,wBAAuB;EAC9B;EAEQ,0BAAuB;AAC7B,UAAM,iBAAiB,KAAK,YAAY,kBAAiB;AAEzD,QAAI,KAAK,OAAO,QAAQ,gBAAgB,KAAK,OAAO,QAAQ,eAAe;AACzE,WAAK,OAAO,SAAS,CAAC,cAAc,CAAC;IACvC;EACF;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEQ,eAAY;AAClB,SAAK,YAAY,aACd,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,UAAO;AAChB,WAAK,cAAc;AACnB,UAAI,MAAM;AACR,aAAK,mBAAmB,KAAK,YAAY,WAAW,CAAC,WAAW,OAAO,CAAC;AACxE,aAAK,cAAc,KAAK,YAAY,WAAW,CAAC,MAAM,OAAO,CAAC;AAC9D,aAAK,iBAAiB,KAAK,YAAY,QAAQ,OAAO;AACtD,aAAK,kBAAkB,KAAK,YAAY,WAAW,CAAC,WAAW,MAAM,OAAO,CAAC;MAC/E;IACF,CAAC;EACL;EAEQ,qBAAkB;AAGxB,SAAK,gBAAgB;MACnB,iBAAiB;MACjB,mBAAmB;;AAGrB,SAAK,eAAe;MAClB,kBAAkB;MAClB,cAAc;;AAGhB,SAAK,UAAU;MACb,WAAW;MACX,WAAW;;AAGb,SAAK,cAAc;MACjB,eAAe;MACf,mBAAmB;;EAEvB;EAEQ,qBAAkB;AAGxB,SAAK,mBAAmB;MACtB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;;;MAErD;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;;;MAErD;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;;;;EAG1D;EAEA,WAAW,OAAa;AACtB,SAAK,OAAO,SAAS,CAAC,KAAK,CAAC;EAC9B;EAEA,gBAAgB,MAAY;AAC1B,UAAM,UAAqC;MACzC,qBAAqB;MACrB,oBAAoB;MACpB,oBAAoB;MACpB,oBAAoB;MACpB,iBAAiB;;AAEnB,WAAO,QAAQ,IAAI,KAAK;EAC1B;EAEA,iBAAiB,MAAY;AAC3B,UAAM,WAAsC;MAC1C,qBAAqB;MACrB,oBAAoB;MACpB,oBAAoB;MACpB,oBAAoB;MACpB,iBAAiB;;AAEnB,WAAO,SAAS,IAAI,KAAK;EAC3B;EAEA,WAAW,WAAe;AACxB,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,gBAAgB,KAAK,OAAO,IAAI,QAAO,IAAK,UAAU,QAAO,MAAO,MAAO,GAAG;AAEpF,QAAI,gBAAgB,IAAI;AACtB,aAAO,GAAG,aAAa;IACzB,WAAW,gBAAgB,MAAM;AAC/B,aAAO,GAAG,KAAK,MAAM,gBAAgB,EAAE,CAAC;IAC1C,OAAO;AACL,aAAO,GAAG,KAAK,MAAM,gBAAgB,IAAI,CAAC;IAC5C;EACF;;qCA9JW,qBAAkB,4BAAA,WAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,cAAA,EAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,cAAA,IAAA,SAAA,SAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,eAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,UAAA,cAAA,WAAA,GAAA,CAAA,qBAAA,IAAA,cAAA,cAAA,GAAA,MAAA,GAAA,CAAA,qBAAA,IAAA,cAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,QAAA,UAAA,GAAA,CAAA,qBAAA,IAAA,cAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,cAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AAlL3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EACF,GAAA,IAAA;AACvB,MAAA,iBAAA,CAAA;AAAyC,MAAA,uBAAA;AAC7C,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,6DAAA;AAA2D,MAAA,uBAAA,EAAI;AAGpE,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,YAAA,CAAA;AAEO,MAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,WAAW,qBAAqB,CAAC;MAAA,CAAA;AACzE,MAAA,yBAAA,GAAA,iBAAA,EAAiB,GAAA,YAAA,CAAA;AACW,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,gBAAA;AAAgB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAC5B,MAAA,yBAAA,IAAA,mBAAA;AAAmB,MAAA,iBAAA,IAAA,8BAAA;AAA4B,MAAA,uBAAA,EAAoB;AAErE,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA,EACQ,IAAA,OAAA,CAAA,EACC,IAAA,QAAA,CAAA;AACK,MAAA,iBAAA,EAAA;AAAiC,MAAA,uBAAA;AAC3D,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAO;AAElD,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,QAAA,CAAA;AACK,MAAA,iBAAA,EAAA;AAAmC,MAAA,uBAAA;AAC7D,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAyB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAO,EACrC,EACF;AAER,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,UAAA,EAAA,EACmB,IAAA,UAAA;AACvB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACvB,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA,EAAS,EACQ;AAIrB,MAAA,qBAAA,IAAA,yCAAA,IAAA,GAAA,YAAA,EAAA,EAG6C,IAAA,yCAAA,IAAA,GAAA,YAAA,EAAA,EA8BL,IAAA,yCAAA,IAAA,GAAA,YAAA,EAAA;AAuD1C,MAAA,uBAAA;AAGA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,IAAA;AAC7B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,UAAA,EAAA,EAC6C,IAAA,UAAA;AACzD,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,eAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAgE,IAAA,UAAA;AACpD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,eAAA;AACF,MAAA,uBAAA;AACA,MAAA,qBAAA,IAAA,uCAAA,GAAA,GAAA,UAAA,EAAA,EAAyE,IAAA,uCAAA,GAAA,GAAA,UAAA,EAAA;AAQ3E,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqC,IAAA,UAAA,EACzB,IAAA,iBAAA,EACS,IAAA,gBAAA;AACC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAiB;AAElD,MAAA,yBAAA,IAAA,kBAAA;AACE,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAgF,IAAA,4CAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AAgBlF,MAAA,uBAAA,EAAmB,EACV,EACP;;;;AA3KA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,kBAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,WAAA,GAAA;AAe8B,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,cAAA,eAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,cAAA,iBAAA;AAgB/B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA;AA8BA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA;AA8BA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;AAwCkD,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAIE,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAcvB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA,SAAA,CAAA,EAAmC,YAAA,aAAA;;oBAlKvE,cAAY,SAAA,MACZ,cAAY,YACZ,eAAa,SAAA,gBAAA,eAAA,gBAAA,eAAA,iBAAA,cACb,iBAAe,WACf,eAAa,OAAA,GAAA,QAAA,CAAA,8wQAAA,EAAA,CAAA;;;sEAqLJ,oBAAkB,CAAA;UA7L9B;uBACW,iBAAe,YACb,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgLT,QAAA,CAAA,8pLAAA,EAAA,CAAA;;;;6EAGU,oBAAkB,EAAA,WAAA,sBAAA,UAAA,0EAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}