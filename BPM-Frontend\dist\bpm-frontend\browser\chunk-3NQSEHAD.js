import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-L52YVPND.js";
import {
  AuthService
} from "./chunk-EDH5VTX4.js";
import {
  MatTabsModule
} from "./chunk-FOR5HVNB.js";
import {
  MatTableModule
} from "./chunk-PJTOMNHB.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardActions,
  MatCardAvatar,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardSubtitle,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import {
  environment
} from "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  Router,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  HttpClient,
  HttpParams,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Injectable,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/core/services/reporting.service.ts
var ReportingService = class _ReportingService {
  http;
  API_URL = `${environment.apiUrl}/api/reporting`;
  constructor(http) {
    this.http = http;
  }
  // General reporting
  getSystemOverviewReport(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/overview`, { params });
  }
  getUserActivityReport(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/user-activity`, { params });
  }
  getWorkflowPerformanceReport(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/workflow-performance`, { params });
  }
  getSystemHealthReport() {
    return this.http.get(`${this.API_URL}/system-health`);
  }
  // Specific analytics
  getRequestTrends(period = "monthly", filters) {
    let params = this.buildFilterParams(filters);
    params = params.set("period", period);
    return this.http.get(`${this.API_URL}/request-trends`, { params });
  }
  getApprovalRates(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/approval-rates`, { params });
  }
  getProcessingTimeAnalysis(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/processing-time`, { params });
  }
  getBottleneckAnalysis(filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/bottlenecks`, { params });
  }
  // Department-specific reports
  getDepartmentReport(departmentId, filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/department/${departmentId}`, { params });
  }
  // Manager-specific reports
  getManagerReport(managerId, filters) {
    let params = this.buildFilterParams(filters);
    return this.http.get(`${this.API_URL}/manager/${managerId}`, { params });
  }
  // Export functionality
  exportReport(reportType, options, filters) {
    let params = this.buildFilterParams(filters);
    params = params.set("format", options.format);
    if (options.includeCharts !== void 0) {
      params = params.set("includeCharts", options.includeCharts.toString());
    }
    if (options.includeRawData !== void 0) {
      params = params.set("includeRawData", options.includeRawData.toString());
    }
    if (options.template) {
      params = params.set("template", options.template);
    }
    return this.http.get(`${this.API_URL}/export/${reportType}`, {
      params,
      responseType: "blob"
    });
  }
  // Custom reports
  createCustomReport(reportConfig) {
    return this.http.post(`${this.API_URL}/custom`, reportConfig);
  }
  getCustomReports() {
    return this.http.get(`${this.API_URL}/custom`);
  }
  runCustomReport(reportId) {
    return this.http.post(`${this.API_URL}/custom/${reportId}/run`, {});
  }
  deleteCustomReport(reportId) {
    return this.http.delete(`${this.API_URL}/custom/${reportId}`);
  }
  // Scheduled reports
  scheduleReport(reportId, schedule) {
    return this.http.post(`${this.API_URL}/schedule/${reportId}`, schedule);
  }
  getScheduledReports() {
    return this.http.get(`${this.API_URL}/scheduled`);
  }
  // Utility methods
  buildFilterParams(filters) {
    let params = new HttpParams();
    if (filters) {
      if (filters.startDate) {
        params = params.set("startDate", filters.startDate.toISOString());
      }
      if (filters.endDate) {
        params = params.set("endDate", filters.endDate.toISOString());
      }
      if (filters.requestType) {
        params = params.set("requestType", filters.requestType);
      }
      if (filters.status) {
        params = params.set("status", filters.status);
      }
      if (filters.department) {
        params = params.set("department", filters.department);
      }
      if (filters.userId) {
        params = params.set("userId", filters.userId);
      }
      if (filters.workflowId) {
        params = params.set("workflowId", filters.workflowId);
      }
    }
    return params;
  }
  static \u0275fac = function ReportingService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReportingService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ReportingService, factory: _ReportingService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReportingService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.ts
function ReportingDashboardComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading dashboard data...");
    \u0275\u0275elementEnd()();
  }
}
function ReportingDashboardComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 5)(1, "div", 6)(2, "mat-card", 7)(3, "mat-card-header")(4, "mat-icon", 8);
    \u0275\u0275text(5, "people");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "mat-card-title");
    \u0275\u0275text(7, "Total Users");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 9);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "div", 10);
    \u0275\u0275text(12, "Registered Users");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(13, "mat-card", 7)(14, "mat-card-header")(15, "mat-icon", 11);
    \u0275\u0275text(16, "account_tree");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "mat-card-title");
    \u0275\u0275text(18, "Active Workflows");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(19, "mat-card-content")(20, "div", 9);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(22, "div", 10);
    \u0275\u0275text(23, "Running Processes");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(24, "mat-card", 7)(25, "mat-card-header")(26, "mat-icon", 12);
    \u0275\u0275text(27, "assignment");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "mat-card-title");
    \u0275\u0275text(29, "Total Requests");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(30, "mat-card-content")(31, "div", 9);
    \u0275\u0275text(32);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "div", 10);
    \u0275\u0275text(34, "All Time");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(35, "mat-card", 7)(36, "mat-card-header")(37, "mat-icon", 13);
    \u0275\u0275text(38, "pending_actions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(39, "mat-card-title");
    \u0275\u0275text(40, "Pending Approvals");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(41, "mat-card-content")(42, "div", 9);
    \u0275\u0275text(43);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "div", 10);
    \u0275\u0275text(45, "Awaiting Action");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(46, "div", 14)(47, "h2");
    \u0275\u0275text(48, "Admin Actions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "div", 15)(50, "mat-card", 16);
    \u0275\u0275listener("click", function ReportingDashboardComponent_div_7_Template_mat_card_click_50_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.navigateTo("/admin/users"));
    });
    \u0275\u0275elementStart(51, "mat-card-header")(52, "mat-icon", 17);
    \u0275\u0275text(53, "manage_accounts");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(54, "mat-card-title");
    \u0275\u0275text(55, "User Management");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(56, "mat-card-subtitle");
    \u0275\u0275text(57, "Manage users, roles, and permissions");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(58, "mat-card-actions")(59, "button", 18)(60, "mat-icon");
    \u0275\u0275text(61, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(62, " Manage Users ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(63, "mat-card", 16);
    \u0275\u0275listener("click", function ReportingDashboardComponent_div_7_Template_mat_card_click_63_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.navigateTo("/admin/workflows"));
    });
    \u0275\u0275elementStart(64, "mat-card-header")(65, "mat-icon", 17);
    \u0275\u0275text(66, "settings");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(67, "mat-card-title");
    \u0275\u0275text(68, "Workflow Designer");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(69, "mat-card-subtitle");
    \u0275\u0275text(70, "Create and configure workflows");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(71, "mat-card-actions")(72, "button", 18)(73, "mat-icon");
    \u0275\u0275text(74, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(75, " Design Workflows ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(76, "mat-card", 16);
    \u0275\u0275listener("click", function ReportingDashboardComponent_div_7_Template_mat_card_click_76_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.navigateTo("/admin/reports"));
    });
    \u0275\u0275elementStart(77, "mat-card-header")(78, "mat-icon", 17);
    \u0275\u0275text(79, "analytics");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(80, "mat-card-title");
    \u0275\u0275text(81, "System Reports");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(82, "mat-card-subtitle");
    \u0275\u0275text(83, "Generate detailed system reports");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(84, "mat-card-actions")(85, "button", 18)(86, "mat-icon");
    \u0275\u0275text(87, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(88, " View Reports ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(89, "mat-card", 16);
    \u0275\u0275listener("click", function ReportingDashboardComponent_div_7_Template_mat_card_click_89_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.navigateTo("/admin/settings"));
    });
    \u0275\u0275elementStart(90, "mat-card-header")(91, "mat-icon", 17);
    \u0275\u0275text(92, "tune");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(93, "mat-card-title");
    \u0275\u0275text(94, "System Settings");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(95, "mat-card-subtitle");
    \u0275\u0275text(96, "Configure system preferences");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(97, "mat-card-actions")(98, "button", 18)(99, "mat-icon");
    \u0275\u0275text(100, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(101, " Settings ");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(102, "div", 19)(103, "h2");
    \u0275\u0275text(104, "Quick Reports");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(105, "mat-card")(106, "mat-card-header")(107, "mat-card-title");
    \u0275\u0275text(108, "Generate Reports");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(109, "mat-card-subtitle");
    \u0275\u0275text(110, "Export system data and analytics");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(111, "mat-card-content")(112, "div", 20)(113, "button", 21)(114, "mat-icon");
    \u0275\u0275text(115, "picture_as_pdf");
    \u0275\u0275elementEnd();
    \u0275\u0275text(116, " Export to PDF ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(117, "button", 22)(118, "mat-icon");
    \u0275\u0275text(119, "table_chart");
    \u0275\u0275elementEnd();
    \u0275\u0275text(120, " Export to Excel ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(121, "button", 23)(122, "mat-icon");
    \u0275\u0275text(123, "bar_chart");
    \u0275\u0275elementEnd();
    \u0275\u0275text(124, " Analytics Report ");
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r1.systemStats.totalUsers);
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate(ctx_r1.systemStats.activeWorkflows);
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate(ctx_r1.systemStats.totalRequests);
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate(ctx_r1.systemStats.pendingApprovals);
  }
}
var ReportingDashboardComponent = class _ReportingDashboardComponent {
  router;
  authService;
  reportingService;
  destroy$ = new Subject();
  isLoading = false;
  currentUser = null;
  // Dashboard stats
  systemStats = {
    totalUsers: 0,
    activeWorkflows: 0,
    totalRequests: 0,
    pendingApprovals: 0
  };
  constructor(router, authService, reportingService) {
    this.router = router;
    this.authService = authService;
    this.reportingService = reportingService;
  }
  ngOnInit() {
    this.loadUserData();
    this.loadSystemStats();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadUserData() {
    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe((user) => {
      this.currentUser = user;
    });
  }
  loadSystemStats() {
    this.isLoading = true;
    setTimeout(() => {
      this.systemStats = {
        totalUsers: 156,
        activeWorkflows: 12,
        totalRequests: 1247,
        pendingApprovals: 23
      };
      this.isLoading = false;
    }, 1e3);
  }
  navigateTo(route) {
    this.router.navigate([route]);
  }
  static \u0275fac = function ReportingDashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReportingDashboardComponent)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(ReportingService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReportingDashboardComponent, selectors: [["app-reporting-dashboard"]], decls: 8, vars: 3, consts: [[1, "reporting-dashboard-container"], [1, "dashboard-header"], ["class", "loading-container", 4, "ngIf"], ["class", "dashboard-content", 4, "ngIf"], [1, "loading-container"], [1, "dashboard-content"], [1, "stats-grid"], [1, "stat-card"], ["mat-card-avatar", "", 1, "users-icon"], [1, "stat-number"], [1, "stat-label"], ["mat-card-avatar", "", 1, "workflows-icon"], ["mat-card-avatar", "", 1, "requests-icon"], ["mat-card-avatar", "", 1, "pending-icon"], [1, "admin-actions"], [1, "action-cards"], [1, "action-card", 3, "click"], ["mat-card-avatar", ""], ["mat-button", "", "color", "primary"], [1, "quick-reports"], [1, "report-buttons"], ["mat-raised-button", "", "color", "primary"], ["mat-raised-button", "", "color", "accent"], ["mat-raised-button", ""]], template: function ReportingDashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "Admin Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(6, ReportingDashboardComponent_div_6_Template, 4, 0, "div", 2)(7, ReportingDashboardComponent_div_7_Template, 125, 4, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate1("Welcome back, ", ctx.currentUser == null ? null : ctx.currentUser.userName, "! Here's your system overview.");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading);
    }
  }, dependencies: [
    CommonModule,
    NgIf,
    RouterModule,
    MatCardModule,
    MatCard,
    MatCardActions,
    MatCardAvatar,
    MatCardContent,
    MatCardHeader,
    MatCardSubtitle,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatTabsModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatProgressSpinner
  ], styles: ["\n\n.reporting-dashboard-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #1976d2;\n  margin-bottom: 0.5rem;\n}\n.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1.1rem;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: #666;\n}\n.dashboard-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n.stats-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n.stat-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 1rem;\n}\n.stat-card[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #1976d2;\n  line-height: 1;\n}\n.stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  margin-top: 0.5rem;\n}\n.stat-card[_ngcontent-%COMP%]   .users-icon[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n}\n.stat-card[_ngcontent-%COMP%]   .workflows-icon[_ngcontent-%COMP%] {\n  background-color: #ff9800;\n  color: white;\n}\n.stat-card[_ngcontent-%COMP%]   .requests-icon[_ngcontent-%COMP%] {\n  background-color: #2196f3;\n  color: white;\n}\n.stat-card[_ngcontent-%COMP%]   .pending-icon[_ngcontent-%COMP%] {\n  background-color: #f44336;\n  color: white;\n}\n.admin-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n.action-cards[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n.action-card[_ngcontent-%COMP%] {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.action-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n.action-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 1rem;\n}\n.action-card[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%] {\n  color: #333;\n  font-weight: 600;\n}\n.action-card[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  margin-top: 0.5rem;\n}\n.action-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%] {\n  padding-top: 1rem;\n}\n.action-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.quick-reports[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n.quick-reports[_ngcontent-%COMP%]   .report-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.quick-reports[_ngcontent-%COMP%]   .report-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  min-width: 150px;\n}\n@media (max-width: 768px) {\n  .reporting-dashboard-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .stats-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .action-cards[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .report-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .report-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n}\n@media (max-width: 480px) {\n  .dashboard-header[_ngcontent-%COMP%] {\n    text-align: center;\n  }\n  .dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.8rem;\n  }\n  .stat-card[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n}\n/*# sourceMappingURL=reporting-dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReportingDashboardComponent, [{
    type: Component,
    args: [{ selector: "app-reporting-dashboard", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatTabsModule,
      MatTableModule,
      MatProgressSpinnerModule
    ], template: `<div class="reporting-dashboard-container">
  <!-- Header Section -->
  <div class="dashboard-header">
    <h1>Admin Dashboard</h1>
    <p>Welcome back, {{currentUser?.userName}}! Here's your system overview.</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading" class="dashboard-content">

    <!-- System Overview Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="users-icon">people</mat-icon>
          <mat-card-title>Total Users</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.totalUsers}}</div>
          <div class="stat-label">Registered Users</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="workflows-icon">account_tree</mat-icon>
          <mat-card-title>Active Workflows</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.activeWorkflows}}</div>
          <div class="stat-label">Running Processes</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="requests-icon">assignment</mat-icon>
          <mat-card-title>Total Requests</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.totalRequests}}</div>
          <div class="stat-label">All Time</div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-icon mat-card-avatar class="pending-icon">pending_actions</mat-icon>
          <mat-card-title>Pending Approvals</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{systemStats.pendingApprovals}}</div>
          <div class="stat-label">Awaiting Action</div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Admin Actions -->
    <div class="admin-actions">
      <h2>Admin Actions</h2>
      <div class="action-cards">
        <mat-card class="action-card" (click)="navigateTo('/admin/users')">
          <mat-card-header>
            <mat-icon mat-card-avatar>manage_accounts</mat-icon>
            <mat-card-title>User Management</mat-card-title>
            <mat-card-subtitle>Manage users, roles, and permissions</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Manage Users
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/workflows')">
          <mat-card-header>
            <mat-icon mat-card-avatar>settings</mat-icon>
            <mat-card-title>Workflow Designer</mat-card-title>
            <mat-card-subtitle>Create and configure workflows</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Design Workflows
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/reports')">
          <mat-card-header>
            <mat-icon mat-card-avatar>analytics</mat-icon>
            <mat-card-title>System Reports</mat-card-title>
            <mat-card-subtitle>Generate detailed system reports</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Reports
            </button>
          </mat-card-actions>
        </mat-card>

        <mat-card class="action-card" (click)="navigateTo('/admin/settings')">
          <mat-card-header>
            <mat-icon mat-card-avatar>tune</mat-icon>
            <mat-card-title>System Settings</mat-card-title>
            <mat-card-subtitle>Configure system preferences</mat-card-subtitle>
          </mat-card-header>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              Settings
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>

    <!-- Quick Reports Section -->
    <div class="quick-reports">
      <h2>Quick Reports</h2>
      <mat-card>
        <mat-card-header>
          <mat-card-title>Generate Reports</mat-card-title>
          <mat-card-subtitle>Export system data and analytics</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="report-buttons">
            <button mat-raised-button color="primary">
              <mat-icon>picture_as_pdf</mat-icon>
              Export to PDF
            </button>
            <button mat-raised-button color="accent">
              <mat-icon>table_chart</mat-icon>
              Export to Excel
            </button>
            <button mat-raised-button>
              <mat-icon>bar_chart</mat-icon>
              Analytics Report
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

  </div>
</div>
`, styles: ["/* src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.scss */\n.reporting-dashboard-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.dashboard-header {\n  margin-bottom: 2rem;\n}\n.dashboard-header h1 {\n  color: #1976d2;\n  margin-bottom: 0.5rem;\n}\n.dashboard-header p {\n  color: #666;\n  font-size: 1.1rem;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n}\n.loading-container p {\n  margin-top: 1rem;\n  color: #666;\n}\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n.stat-card .mat-card-header {\n  padding-bottom: 1rem;\n}\n.stat-card .stat-number {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #1976d2;\n  line-height: 1;\n}\n.stat-card .stat-label {\n  color: #666;\n  font-size: 0.9rem;\n  margin-top: 0.5rem;\n}\n.stat-card .users-icon {\n  background-color: #4caf50;\n  color: white;\n}\n.stat-card .workflows-icon {\n  background-color: #ff9800;\n  color: white;\n}\n.stat-card .requests-icon {\n  background-color: #2196f3;\n  color: white;\n}\n.stat-card .pending-icon {\n  background-color: #f44336;\n  color: white;\n}\n.admin-actions h2 {\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n.action-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n}\n.action-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.action-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n.action-card .mat-card-header {\n  padding-bottom: 1rem;\n}\n.action-card .mat-card-title {\n  color: #333;\n  font-weight: 600;\n}\n.action-card .mat-card-subtitle {\n  color: #666;\n  margin-top: 0.5rem;\n}\n.action-card .mat-card-actions {\n  padding-top: 1rem;\n}\n.action-card .mat-card-actions button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.quick-reports h2 {\n  color: #333;\n  margin-bottom: 1.5rem;\n}\n.quick-reports .report-buttons {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.quick-reports .report-buttons button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  min-width: 150px;\n}\n@media (max-width: 768px) {\n  .reporting-dashboard-container {\n    padding: 1rem;\n  }\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  .action-cards {\n    grid-template-columns: 1fr;\n  }\n  .report-buttons {\n    flex-direction: column;\n  }\n  .report-buttons button {\n    width: 100%;\n  }\n}\n@media (max-width: 480px) {\n  .dashboard-header {\n    text-align: center;\n  }\n  .dashboard-header h1 {\n    font-size: 1.8rem;\n  }\n  .stat-card .stat-number {\n    font-size: 2rem;\n  }\n}\n/*# sourceMappingURL=reporting-dashboard.component.css.map */\n"] }]
  }], () => [{ type: Router }, { type: AuthService }, { type: ReportingService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReportingDashboardComponent, { className: "ReportingDashboardComponent", filePath: "src/app/features/dashboard/components/reporting-dashboard/reporting-dashboard.component.ts", lineNumber: 31 });
})();
export {
  ReportingDashboardComponent
};
//# sourceMappingURL=chunk-3NQSEHAD.js.map
