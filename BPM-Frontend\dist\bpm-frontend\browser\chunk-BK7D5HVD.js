import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  MatCardModule
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  Mat<PERSON>utton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/shared/components/unauthorized/unauthorized.component.ts
var UnauthorizedComponent = class _UnauthorizedComponent {
  static \u0275fac = function UnauthorizedComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UnauthorizedComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UnauthorizedComponent, selectors: [["app-unauthorized"]], decls: 21, vars: 0, consts: [[1, "unauthorized-container"], [1, "unauthorized-card"], [1, "error-icon"], [1, "actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/dashboard"], ["mat-button", "", "routerLink", "/auth/login"]], template: function UnauthorizedComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-content")(3, "div", 2)(4, "mat-icon");
      \u0275\u0275text(5, "block");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "h1");
      \u0275\u0275text(7, "Access Denied");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "p");
      \u0275\u0275text(9, "You don't have permission to access this page.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p");
      \u0275\u0275text(11, "Please contact your administrator if you believe this is an error.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 3)(13, "button", 4)(14, "mat-icon");
      \u0275\u0275text(15, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275text(16, " Go to Dashboard ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "button", 5)(18, "mat-icon");
      \u0275\u0275text(19, "login");
      \u0275\u0275elementEnd();
      \u0275\u0275text(20, " Login as Different User ");
      \u0275\u0275elementEnd()()()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    RouterLink,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent
  ], styles: ["\n\n.unauthorized-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.unauthorized-card[_ngcontent-%COMP%] {\n  max-width: 500px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.error-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #f44336;\n}\nh1[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n}\np[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.6;\n}\n.actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=unauthorized.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UnauthorizedComponent, [{
    type: Component,
    args: [{ selector: "app-unauthorized", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule
    ], template: `
    <div class="unauthorized-container">
      <mat-card class="unauthorized-card">
        <mat-card-content>
          <div class="error-icon">
            <mat-icon>block</mat-icon>
          </div>
          <h1>Access Denied</h1>
          <p>You don't have permission to access this page.</p>
          <p>Please contact your administrator if you believe this is an error.</p>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/dashboard">
              <mat-icon>home</mat-icon>
              Go to Dashboard
            </button>
            <button mat-button routerLink="/auth/login">
              <mat-icon>login</mat-icon>
              Login as Different User
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;ab7f0bed88a0cac0eac21000197b08e29ed4581031b0487f8bf15d18cd4050ac;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/shared/components/unauthorized/unauthorized.component.ts */\n.unauthorized-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.unauthorized-card {\n  max-width: 500px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon {\n  margin-bottom: 1rem;\n}\n.error-icon mat-icon {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #f44336;\n}\nh1 {\n  color: #333;\n  margin-bottom: 1rem;\n}\np {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.6;\n}\n.actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=unauthorized.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UnauthorizedComponent, { className: "UnauthorizedComponent", filePath: "src/app/shared/components/unauthorized/unauthorized.component.ts", lineNumber: 95 });
})();
export {
  UnauthorizedComponent
};
//# sourceMappingURL=chunk-BK7D5HVD.js.map
