import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  MatCardModule
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  <PERSON><PERSON>utton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/features/admin/components/admin-not-found/admin-not-found.component.ts
var AdminNotFoundComponent = class _AdminNotFoundComponent {
  constructor() {
  }
  static \u0275fac = function AdminNotFoundComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminNotFoundComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminNotFoundComponent, selectors: [["app-admin-not-found"]], decls: 37, vars: 0, consts: [[1, "not-found-container"], [1, "not-found-card"], [1, "error-icon"], [1, "suggestions"], [1, "actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/admin/users"], ["mat-raised-button", "", "color", "accent", "routerLink", "/admin/workflow-designer"], ["mat-button", "", "routerLink", "/dashboard"]], template: function AdminNotFoundComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-content")(3, "div", 2)(4, "mat-icon");
      \u0275\u0275text(5, "admin_panel_settings");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "h1");
      \u0275\u0275text(7, "Admin Page Not Found");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "p");
      \u0275\u0275text(9, "The administrative page you're looking for doesn't exist or has been moved.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 3)(11, "h3");
      \u0275\u0275text(12, "Available Admin Functions:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "ul")(14, "li");
      \u0275\u0275text(15, "User Management - Manage system users and permissions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "li");
      \u0275\u0275text(17, "Role Management - Configure user roles and access levels");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "li");
      \u0275\u0275text(19, "Workflow Designer - Create and modify business workflows");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "li");
      \u0275\u0275text(21, "System Settings - Configure application settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "li");
      \u0275\u0275text(23, "Reports - View system analytics and reports");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "div", 4)(25, "button", 5)(26, "mat-icon");
      \u0275\u0275text(27, "people");
      \u0275\u0275elementEnd();
      \u0275\u0275text(28, " User Management ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "button", 6)(30, "mat-icon");
      \u0275\u0275text(31, "account_tree");
      \u0275\u0275elementEnd();
      \u0275\u0275text(32, " Workflow Designer ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "button", 7)(34, "mat-icon");
      \u0275\u0275text(35, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275text(36, " Dashboard ");
      \u0275\u0275elementEnd()()()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    RouterLink,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent
  ], styles: ["\n\n.not-found-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card[_ngcontent-%COMP%] {\n  max-width: 600px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.error-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #ff9800;\n}\nh1[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 2rem;\n}\np[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n  font-size: 1.1rem;\n}\n.suggestions[_ngcontent-%COMP%] {\n  text-align: left;\n  margin: 2rem 0;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n}\n.suggestions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-left: 1.5rem;\n}\n.suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n.actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .not-found-card[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=admin-not-found.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminNotFoundComponent, [{
    type: Component,
    args: [{ selector: "app-admin-not-found", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule
    ], template: `
    <div class="not-found-container">
      <mat-card class="not-found-card">
        <mat-card-content>
          <div class="error-icon">
            <mat-icon>admin_panel_settings</mat-icon>
          </div>
          <h1>Admin Page Not Found</h1>
          <p>The administrative page you're looking for doesn't exist or has been moved.</p>
          <div class="suggestions">
            <h3>Available Admin Functions:</h3>
            <ul>
              <li>User Management - Manage system users and permissions</li>
              <li>Role Management - Configure user roles and access levels</li>
              <li>Workflow Designer - Create and modify business workflows</li>
              <li>System Settings - Configure application settings</li>
              <li>Reports - View system analytics and reports</li>
            </ul>
          </div>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/admin/users">
              <mat-icon>people</mat-icon>
              User Management
            </button>
            <button mat-raised-button color="accent" routerLink="/admin/workflow-designer">
              <mat-icon>account_tree</mat-icon>
              Workflow Designer
            </button>
            <button mat-button routerLink="/dashboard">
              <mat-icon>home</mat-icon>
              Dashboard
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;672e8c98389ec8715310309d84a433fce0f79aed93f756a5c8fd04404604266d;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/admin/components/admin-not-found/admin-not-found.component.ts */\n.not-found-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card {\n  max-width: 600px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon {\n  margin-bottom: 1rem;\n}\n.error-icon mat-icon {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #ff9800;\n}\nh1 {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 2rem;\n}\np {\n  color: #666;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n  font-size: 1.1rem;\n}\n.suggestions {\n  text-align: left;\n  margin: 2rem 0;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.suggestions h3 {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n}\n.suggestions ul {\n  margin: 0;\n  padding-left: 1.5rem;\n}\n.suggestions li {\n  color: #666;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n.actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions {\n    flex-direction: column;\n  }\n  .not-found-card {\n    padding: 1rem;\n  }\n  h1 {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=admin-not-found.component.css.map */\n"] }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminNotFoundComponent, { className: "AdminNotFoundComponent", filePath: "src/app/features/admin/components/admin-not-found/admin-not-found.component.ts", lineNumber: 143 });
})();
export {
  AdminNotFoundComponent
};
//# sourceMappingURL=chunk-AL7MW7VA.js.map
