{"version": 3, "sources": ["src/app/features/workflows/components/workflows-not-found/workflows-not-found.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\n\n@Component({\n  selector: 'app-workflows-not-found',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule\n  ],\n  template: `\n    <div class=\"not-found-container\">\n      <mat-card class=\"not-found-card\">\n        <mat-card-content>\n          <div class=\"error-icon\">\n            <mat-icon>device_hub</mat-icon>\n          </div>\n          <h1>Workflow Not Found</h1>\n          <p>The workflow you're looking for doesn't exist or has been removed.</p>\n          <div class=\"suggestions\">\n            <h3>What you can do:</h3>\n            <ul>\n              <li>Check if the workflow ID is correct</li>\n              <li>Browse available workflows from the main list</li>\n              <li>Create a new workflow using the designer</li>\n              <li>Contact your administrator for assistance</li>\n            </ul>\n          </div>\n          <div class=\"actions\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/workflows\">\n              <mat-icon>list</mat-icon>\n              View All Workflows\n            </button>\n            <button mat-raised-button color=\"accent\" routerLink=\"/workflows/designer\">\n              <mat-icon>add</mat-icon>\n              Create Workflow\n            </button>\n            <button mat-button routerLink=\"/dashboard\">\n              <mat-icon>home</mat-icon>\n              Dashboard\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .not-found-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .not-found-card {\n      max-width: 600px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #4caf50;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 2rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1.5rem;\n      line-height: 1.6;\n      font-size: 1.1rem;\n    }\n\n    .suggestions {\n      text-align: left;\n      margin: 2rem 0;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .suggestions h3 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 1.2rem;\n    }\n\n    .suggestions ul {\n      margin: 0;\n      padding-left: 1.5rem;\n    }\n\n    .suggestions li {\n      color: #666;\n      margin-bottom: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .actions {\n        flex-direction: column;\n      }\n      \n      .not-found-card {\n        padding: 1rem;\n      }\n      \n      h1 {\n        font-size: 1.5rem;\n      }\n    }\n  `]\n})\nexport class WorkflowsNotFoundComponent {\n  constructor() {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6IM,IAAO,6BAAP,MAAO,4BAA0B;EACrC,cAAA;EAAe;;qCADJ,6BAA0B;EAAA;yEAA1B,6BAA0B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,UAAA,cAAA,qBAAA,GAAA,CAAA,cAAA,IAAA,cAAA,YAAA,CAAA,GAAA,UAAA,SAAA,oCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA3HnC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,YAAA,CAAA,EACE,GAAA,kBAAA,EACb,GAAA,OAAA,CAAA,EACQ,GAAA,UAAA;AACZ,MAAA,iBAAA,GAAA,YAAA;AAAU,MAAA,uBAAA,EAAW;AAEjC,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,oBAAA;AAAkB,MAAA,uBAAA;AACtB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,oEAAA;AAAkE,MAAA,uBAAA;AACrE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,MAAA,iBAAA,IAAA,qCAAA;AAAmC,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,0CAAA;AAAwC,MAAA,uBAAA;AAC5C,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,2CAAA;AAAyC,MAAA,uBAAA,EAAK,EAC/C;AAEP,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA,EAC+C,IAAA,UAAA;AACtD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,sBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA0E,IAAA,UAAA;AAC9D,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACb,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA2C,IAAA,UAAA;AAC/B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,aAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACW,EACV;;;IAvCb;IACA;IAAY;IACZ;IAAe;IACf;IAAa;IACb;IAAa;IAAA;EAAA,GAAA,QAAA,CAAA,yoDAAA,EAAA,CAAA;;;sEA8HJ,4BAA0B,CAAA;UAtItC;uBACW,2BAAyB,YACvB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCT,QAAA,CAAA,yhDAAA,EAAA,CAAA;;;;6EAyFU,4BAA0B,EAAA,WAAA,8BAAA,UAAA,8FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}