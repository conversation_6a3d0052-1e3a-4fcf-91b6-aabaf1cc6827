import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatCardModule,
  MatCardSubtitle,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MatButtonModule,
  MatIconButton
} from "./chunk-5XTAYFTV.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/shared/components/icon-test/icon-test.component.ts
var IconTestComponent = class _IconTestComponent {
  static \u0275fac = function IconTestComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _IconTestComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _IconTestComponent, selectors: [["app-icon-test"]], decls: 140, vars: 0, consts: [[1, "icon-test-container"], [1, "icon-grid"], [1, "icon-section"], [1, "icon-row"], [1, "button-row"], ["mat-raised-button", "", "color", "primary"], ["mat-raised-button", "", "color", "accent"], ["mat-raised-button", "", "color", "warn"], ["mat-icon-button", ""], [1, "success-icon"], [1, "warning-icon"], [1, "error-icon"], [1, "info-icon"], [1, "icon-18"], [1, "icon-24"], [1, "icon-36"], [1, "icon-48"], [1, "material-icons"], [1, "mdi", "mdi-home"], [1, "mdi", "mdi-view-dashboard"], [1, "mdi", "mdi-account"], [1, "mdi", "mdi-cog"]], template: function IconTestComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Icon Test Page");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "mat-card-subtitle");
      \u0275\u0275text(6, "Testing Material Icons across different platforms");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "mat-card-content")(8, "div", 1)(9, "div", 2)(10, "h3");
      \u0275\u0275text(11, "Material Icons (Standard)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 3)(13, "mat-icon");
      \u0275\u0275text(14, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "mat-icon");
      \u0275\u0275text(16, "dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "mat-icon");
      \u0275\u0275text(18, "person");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "mat-icon");
      \u0275\u0275text(20, "settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "mat-icon");
      \u0275\u0275text(22, "notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "mat-icon");
      \u0275\u0275text(24, "business");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "mat-icon");
      \u0275\u0275text(26, "assignment");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "mat-icon");
      \u0275\u0275text(28, "account_circle");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(29, "div", 2)(30, "h3");
      \u0275\u0275text(31, "Material Icons in Buttons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "div", 4)(33, "button", 5)(34, "mat-icon");
      \u0275\u0275text(35, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(36, " Add New ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "button", 6)(38, "mat-icon");
      \u0275\u0275text(39, "edit");
      \u0275\u0275elementEnd();
      \u0275\u0275text(40, " Edit ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(41, "button", 7)(42, "mat-icon");
      \u0275\u0275text(43, "delete");
      \u0275\u0275elementEnd();
      \u0275\u0275text(44, " Delete ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "button", 8)(46, "mat-icon");
      \u0275\u0275text(47, "more_vert");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(48, "div", 2)(49, "h3");
      \u0275\u0275text(50, "Navigation Icons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "div", 3)(52, "mat-icon");
      \u0275\u0275text(53, "menu");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "mat-icon");
      \u0275\u0275text(55, "arrow_back");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "mat-icon");
      \u0275\u0275text(57, "arrow_forward");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "mat-icon");
      \u0275\u0275text(59, "expand_more");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "mat-icon");
      \u0275\u0275text(61, "expand_less");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(62, "mat-icon");
      \u0275\u0275text(63, "close");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(64, "mat-icon");
      \u0275\u0275text(65, "search");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(66, "mat-icon");
      \u0275\u0275text(67, "filter_list");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(68, "div", 2)(69, "h3");
      \u0275\u0275text(70, "Status Icons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(71, "div", 3)(72, "mat-icon", 9);
      \u0275\u0275text(73, "check_circle");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "mat-icon", 10);
      \u0275\u0275text(75, "warning");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "mat-icon", 11);
      \u0275\u0275text(77, "error");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(78, "mat-icon", 12);
      \u0275\u0275text(79, "info");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(80, "mat-icon");
      \u0275\u0275text(81, "pending");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(82, "mat-icon");
      \u0275\u0275text(83, "schedule");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(84, "mat-icon");
      \u0275\u0275text(85, "done");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(86, "mat-icon");
      \u0275\u0275text(87, "cancel");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(88, "div", 2)(89, "h3");
      \u0275\u0275text(90, "Business Process Icons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(91, "div", 3)(92, "mat-icon");
      \u0275\u0275text(93, "work");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(94, "mat-icon");
      \u0275\u0275text(95, "assignment_turned_in");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(96, "mat-icon");
      \u0275\u0275text(97, "approval");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(98, "mat-icon");
      \u0275\u0275text(99, "supervisor_account");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(100, "mat-icon");
      \u0275\u0275text(101, "people");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(102, "mat-icon");
      \u0275\u0275text(103, "analytics");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(104, "mat-icon");
      \u0275\u0275text(105, "account_tree");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(106, "mat-icon");
      \u0275\u0275text(107, "timeline");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(108, "div", 2)(109, "h3");
      \u0275\u0275text(110, "Different Sizes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(111, "div", 3)(112, "mat-icon", 13);
      \u0275\u0275text(113, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(114, "mat-icon", 14);
      \u0275\u0275text(115, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(116, "mat-icon", 15);
      \u0275\u0275text(117, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(118, "mat-icon", 16);
      \u0275\u0275text(119, "home");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(120, "div", 2)(121, "h3");
      \u0275\u0275text(122, "Raw HTML Icons (Fallback Test)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(123, "div", 3)(124, "i", 17);
      \u0275\u0275text(125, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(126, "i", 17);
      \u0275\u0275text(127, "dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(128, "i", 17);
      \u0275\u0275text(129, "person");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(130, "i", 17);
      \u0275\u0275text(131, "settings");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(132, "div", 2)(133, "h3");
      \u0275\u0275text(134, "MDI Icons (Alternative)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(135, "div", 3);
      \u0275\u0275element(136, "i", 18)(137, "i", 19)(138, "i", 20)(139, "i", 21);
      \u0275\u0275elementEnd()()()()()();
    }
  }, dependencies: [
    CommonModule,
    MatIconModule,
    MatIcon,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardSubtitle,
    MatCardTitle
  ], styles: ["\n\n.icon-test-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.icon-grid[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n.icon-section[_ngcontent-%COMP%] {\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  padding: 20px;\n  background: #fafafa;\n}\n.icon-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-weight: 500;\n}\n.icon-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.button-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  flex-wrap: wrap;\n}\nmat-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  width: 24px;\n  height: 24px;\n}\n.icon-18[_ngcontent-%COMP%] {\n  font-size: 18px !important;\n  width: 18px !important;\n  height: 18px !important;\n}\n.icon-24[_ngcontent-%COMP%] {\n  font-size: 24px !important;\n  width: 24px !important;\n  height: 24px !important;\n}\n.icon-36[_ngcontent-%COMP%] {\n  font-size: 36px !important;\n  width: 36px !important;\n  height: 36px !important;\n}\n.icon-48[_ngcontent-%COMP%] {\n  font-size: 48px !important;\n  width: 48px !important;\n  height: 48px !important;\n}\n.success-icon[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n.warning-icon[_ngcontent-%COMP%] {\n  color: #ff9800;\n}\n.error-icon[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n.info-icon[_ngcontent-%COMP%] {\n  color: #2196f3;\n}\n.material-icons[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #666;\n}\n.mdi[_ngcontent-%COMP%] {\n  font-size: 24px;\n  color: #666;\n}\n@media (max-width: 768px) {\n  .icon-row[_ngcontent-%COMP%], \n   .button-row[_ngcontent-%COMP%] {\n    gap: 10px;\n  }\n  .icon-test-container[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n}\n/*# sourceMappingURL=icon-test.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IconTestComponent, [{
    type: Component,
    args: [{ selector: "app-icon-test", standalone: true, imports: [
      CommonModule,
      MatIconModule,
      MatButtonModule,
      MatCardModule
    ], template: `
    <div class="icon-test-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Icon Test Page</mat-card-title>
          <mat-card-subtitle>Testing Material Icons across different platforms</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="icon-grid">
            <div class="icon-section">
              <h3>Material Icons (Standard)</h3>
              <div class="icon-row">
                <mat-icon>home</mat-icon>
                <mat-icon>dashboard</mat-icon>
                <mat-icon>person</mat-icon>
                <mat-icon>settings</mat-icon>
                <mat-icon>notifications</mat-icon>
                <mat-icon>business</mat-icon>
                <mat-icon>assignment</mat-icon>
                <mat-icon>account_circle</mat-icon>
              </div>
            </div>

            <div class="icon-section">
              <h3>Material Icons in Buttons</h3>
              <div class="button-row">
                <button mat-raised-button color="primary">
                  <mat-icon>add</mat-icon>
                  Add New
                </button>
                <button mat-raised-button color="accent">
                  <mat-icon>edit</mat-icon>
                  Edit
                </button>
                <button mat-raised-button color="warn">
                  <mat-icon>delete</mat-icon>
                  Delete
                </button>
                <button mat-icon-button>
                  <mat-icon>more_vert</mat-icon>
                </button>
              </div>
            </div>

            <div class="icon-section">
              <h3>Navigation Icons</h3>
              <div class="icon-row">
                <mat-icon>menu</mat-icon>
                <mat-icon>arrow_back</mat-icon>
                <mat-icon>arrow_forward</mat-icon>
                <mat-icon>expand_more</mat-icon>
                <mat-icon>expand_less</mat-icon>
                <mat-icon>close</mat-icon>
                <mat-icon>search</mat-icon>
                <mat-icon>filter_list</mat-icon>
              </div>
            </div>

            <div class="icon-section">
              <h3>Status Icons</h3>
              <div class="icon-row">
                <mat-icon class="success-icon">check_circle</mat-icon>
                <mat-icon class="warning-icon">warning</mat-icon>
                <mat-icon class="error-icon">error</mat-icon>
                <mat-icon class="info-icon">info</mat-icon>
                <mat-icon>pending</mat-icon>
                <mat-icon>schedule</mat-icon>
                <mat-icon>done</mat-icon>
                <mat-icon>cancel</mat-icon>
              </div>
            </div>

            <div class="icon-section">
              <h3>Business Process Icons</h3>
              <div class="icon-row">
                <mat-icon>work</mat-icon>
                <mat-icon>assignment_turned_in</mat-icon>
                <mat-icon>approval</mat-icon>
                <mat-icon>supervisor_account</mat-icon>
                <mat-icon>people</mat-icon>
                <mat-icon>analytics</mat-icon>
                <mat-icon>account_tree</mat-icon>
                <mat-icon>timeline</mat-icon>
              </div>
            </div>

            <div class="icon-section">
              <h3>Different Sizes</h3>
              <div class="icon-row">
                <mat-icon class="icon-18">home</mat-icon>
                <mat-icon class="icon-24">home</mat-icon>
                <mat-icon class="icon-36">home</mat-icon>
                <mat-icon class="icon-48">home</mat-icon>
              </div>
            </div>

            <div class="icon-section">
              <h3>Raw HTML Icons (Fallback Test)</h3>
              <div class="icon-row">
                <i class="material-icons">home</i>
                <i class="material-icons">dashboard</i>
                <i class="material-icons">person</i>
                <i class="material-icons">settings</i>
              </div>
            </div>

            <div class="icon-section">
              <h3>MDI Icons (Alternative)</h3>
              <div class="icon-row">
                <i class="mdi mdi-home"></i>
                <i class="mdi mdi-view-dashboard"></i>
                <i class="mdi mdi-account"></i>
                <i class="mdi mdi-cog"></i>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;cf62d57113c25bf268f004b0473127a200d42dbc72323c957901b8735ed7c266;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/shared/components/icon-test/icon-test.component.ts */\n.icon-test-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.icon-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n.icon-section {\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  padding: 20px;\n  background: #fafafa;\n}\n.icon-section h3 {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-weight: 500;\n}\n.icon-row {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.button-row {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  flex-wrap: wrap;\n}\nmat-icon {\n  font-size: 24px;\n  width: 24px;\n  height: 24px;\n}\n.icon-18 {\n  font-size: 18px !important;\n  width: 18px !important;\n  height: 18px !important;\n}\n.icon-24 {\n  font-size: 24px !important;\n  width: 24px !important;\n  height: 24px !important;\n}\n.icon-36 {\n  font-size: 36px !important;\n  width: 36px !important;\n  height: 36px !important;\n}\n.icon-48 {\n  font-size: 48px !important;\n  width: 48px !important;\n  height: 48px !important;\n}\n.success-icon {\n  color: #4caf50;\n}\n.warning-icon {\n  color: #ff9800;\n}\n.error-icon {\n  color: #f44336;\n}\n.info-icon {\n  color: #2196f3;\n}\n.material-icons {\n  font-size: 24px;\n  color: #666;\n}\n.mdi {\n  font-size: 24px;\n  color: #666;\n}\n@media (max-width: 768px) {\n  .icon-row,\n  .button-row {\n    gap: 10px;\n  }\n  .icon-test-container {\n    padding: 10px;\n  }\n}\n/*# sourceMappingURL=icon-test.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(IconTestComponent, { className: "IconTestComponent", filePath: "src/app/shared/components/icon-test/icon-test.component.ts", lineNumber: 245 });
})();
export {
  IconTestComponent
};
//# sourceMappingURL=chunk-NEBKQYKJ.js.map
