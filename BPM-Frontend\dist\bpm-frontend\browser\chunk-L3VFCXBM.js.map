{"version": 3, "sources": ["src/app/features/dashboard/dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n// All components are now loaded dynamically\n\n\nconst routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/dashboard/dashboard.component').then(c => c.DashboardComponent)\n  },\n  {\n    path: 'employee',\n    loadComponent: () => import('./components/employee-dashboard/employee-dashboard.component').then(c => c.EmployeeDashboardComponent)\n  },\n  {\n    path: 'manager',\n    loadComponent: () => import('./components/manager-dashboard/manager-dashboard.component').then(c => c.ManagerDashboardComponent),\n    data: { roles: ['Manager', 'Admin'] }\n  },\n  {\n    path: 'hr',\n    loadComponent: () => import('./components/hr-dashboard/hr-dashboard.component').then(c => c.HRDashboardComponent),\n    data: { roles: ['HR', 'Admin'] }\n  },\n  {\n    path: 'reports',\n    loadComponent: () => import('./components/reporting-dashboard/reporting-dashboard.component').then(c => c.ReportingDashboardComponent),\n    data: { roles: ['Manager', 'HR', 'Admin'] }\n  },\n  {\n    path: 'reporting',\n    loadComponent: () => import('./components/reporting-dashboard/reporting-dashboard.component').then(c => c.ReportingDashboardComponent),\n    data: { roles: ['Manager', 'HR', 'Admin'] }\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class DashboardModule { }"], "mappings": ";;;;;;;;;;;;AAMA,IAAM,SAAS;EACb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,kBAAkB;;EAE1G;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA8D,EAAE,KAAK,OAAK,EAAE,0BAA0B;;EAEpI;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA4D,EAAE,KAAK,OAAK,EAAE,yBAAyB;IAC/H,MAAM,EAAE,OAAO,CAAC,WAAW,OAAO,EAAC;;EAErC;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,oBAAoB;IAChH,MAAM,EAAE,OAAO,CAAC,MAAM,OAAO,EAAC;;EAEhC;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAgE,EAAE,KAAK,OAAK,EAAE,2BAA2B;IACrI,MAAM,EAAE,OAAO,CAAC,WAAW,MAAM,OAAO,EAAC;;EAE3C;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAgE,EAAE,KAAK,OAAK,EAAE,2BAA2B;IACrI,MAAM,EAAE,OAAO,CAAC,WAAW,MAAM,OAAO,EAAC;;;AAQvC,IAAO,kBAAP,MAAO,iBAAe;;qCAAf,kBAAe;EAAA;wEAAf,iBAAe,CAAA;4EAHhB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,iBAAe,CAAA;UAJ3B;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;", "names": []}