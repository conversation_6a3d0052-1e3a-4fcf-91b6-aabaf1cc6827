import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  MatCardModule
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  <PERSON><PERSON><PERSON>on,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/shared/components/not-found/not-found.component.ts
var NotFoundComponent = class _NotFoundComponent {
  goBack() {
    window.history.back();
  }
  static \u0275fac = function NotFoundComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotFoundComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotFoundComponent, selectors: [["app-not-found"]], decls: 21, vars: 0, consts: [[1, "not-found-container"], [1, "not-found-card"], [1, "error-code"], [1, "error-icon"], [1, "actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/dashboard"], ["mat-button", "", 3, "click"]], template: function NotFoundComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-content")(3, "div", 2);
      \u0275\u0275text(4, "404");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "div", 3)(6, "mat-icon");
      \u0275\u0275text(7, "search_off");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "h1");
      \u0275\u0275text(9, "Page Not Found");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p");
      \u0275\u0275text(11, "The page you're looking for doesn't exist or has been moved.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 4)(13, "button", 5)(14, "mat-icon");
      \u0275\u0275text(15, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275text(16, " Go to Dashboard ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "button", 6);
      \u0275\u0275listener("click", function NotFoundComponent_Template_button_click_17_listener() {
        return ctx.goBack();
      });
      \u0275\u0275elementStart(18, "mat-icon");
      \u0275\u0275text(19, "arrow_back");
      \u0275\u0275elementEnd();
      \u0275\u0275text(20, " Go Back ");
      \u0275\u0275elementEnd()()()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    RouterLink,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent
  ], styles: ["\n\n.not-found-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card[_ngcontent-%COMP%] {\n  max-width: 500px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-code[_ngcontent-%COMP%] {\n  font-size: 6rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 1rem;\n  line-height: 1;\n}\n.error-icon[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.error-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  color: #666;\n}\nh1[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n}\np[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.6;\n}\n.actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .error-code[_ngcontent-%COMP%] {\n    font-size: 4rem;\n  }\n  .actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=not-found.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotFoundComponent, [{
    type: Component,
    args: [{ selector: "app-not-found", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule
    ], template: `
    <div class="not-found-container">
      <mat-card class="not-found-card">
        <mat-card-content>
          <div class="error-code">404</div>
          <div class="error-icon">
            <mat-icon>search_off</mat-icon>
          </div>
          <h1>Page Not Found</h1>
          <p>The page you're looking for doesn't exist or has been moved.</p>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/dashboard">
              <mat-icon>home</mat-icon>
              Go to Dashboard
            </button>
            <button mat-button (click)="goBack()">
              <mat-icon>arrow_back</mat-icon>
              Go Back
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;dcfa7b19f3a267e6fee94995c20bc9ef605b6c8b682481c7e0dad41c11215496;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/shared/components/not-found/not-found.component.ts */\n.not-found-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card {\n  max-width: 500px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-code {\n  font-size: 6rem;\n  font-weight: bold;\n  color: #667eea;\n  margin-bottom: 1rem;\n  line-height: 1;\n}\n.error-icon {\n  margin-bottom: 1rem;\n}\n.error-icon mat-icon {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  color: #666;\n}\nh1 {\n  color: #333;\n  margin-bottom: 1rem;\n}\np {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.6;\n}\n.actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .error-code {\n    font-size: 4rem;\n  }\n  .actions {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=not-found.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotFoundComponent, { className: "NotFoundComponent", filePath: "src/app/shared/components/not-found/not-found.component.ts", lineNumber: 107 });
})();
export {
  NotFoundComponent
};
//# sourceMappingURL=chunk-ZXTC3ICP.js.map
