import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/features/admin/components/workflow-designer/workflow-designer.component.ts
var WorkflowDesignerComponent = class _WorkflowDesignerComponent {
  static \u0275fac = function WorkflowDesignerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowDesignerComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowDesignerComponent, selectors: [["app-workflow-designer"]], decls: 2, vars: 0, template: function WorkflowDesignerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "p");
      \u0275\u0275text(1, "workflow-designer works!");
      \u0275\u0275elementEnd();
    }
  }, encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowDesignerComponent, [{
    type: Component,
    args: [{ selector: "app-workflow-designer", template: "<p>workflow-designer works!</p>\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowDesignerComponent, { className: "WorkflowDesignerComponent", filePath: "src/app/features/admin/components/workflow-designer/workflow-designer.component.ts", lineNumber: 8 });
})();
export {
  WorkflowDesignerComponent
};
//# sourceMappingURL=chunk-ULPAAHOV.js.map
