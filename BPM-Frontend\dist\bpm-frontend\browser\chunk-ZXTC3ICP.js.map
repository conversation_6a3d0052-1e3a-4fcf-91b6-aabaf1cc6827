{"version": 3, "sources": ["src/app/shared/components/not-found/not-found.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\n\n@Component({\n  selector: 'app-not-found',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule\n  ],\n  template: `\n    <div class=\"not-found-container\">\n      <mat-card class=\"not-found-card\">\n        <mat-card-content>\n          <div class=\"error-code\">404</div>\n          <div class=\"error-icon\">\n            <mat-icon>search_off</mat-icon>\n          </div>\n          <h1>Page Not Found</h1>\n          <p>The page you're looking for doesn't exist or has been moved.</p>\n          <div class=\"actions\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/dashboard\">\n              <mat-icon>home</mat-icon>\n              Go to Dashboard\n            </button>\n            <button mat-button (click)=\"goBack()\">\n              <mat-icon>arrow_back</mat-icon>\n              Go Back\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .not-found-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .not-found-card {\n      max-width: 500px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-code {\n      font-size: 6rem;\n      font-weight: bold;\n      color: #667eea;\n      margin-bottom: 1rem;\n      line-height: 1;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      color: #666;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1rem;\n      line-height: 1.6;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .error-code {\n        font-size: 4rem;\n      }\n      \n      .actions {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class NotFoundComponent {\n  goBack(): void {\n    window.history.back();\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0GM,IAAO,oBAAP,MAAO,mBAAiB;EAC5B,SAAM;AACJ,WAAO,QAAQ,KAAI;EACrB;;qCAHW,oBAAiB;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,YAAA,GAAA,CAAA,cAAA,IAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAxF1B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,YAAA,CAAA,EACE,GAAA,kBAAA,EACb,GAAA,OAAA,CAAA;AACQ,MAAA,iBAAA,GAAA,KAAA;AAAG,MAAA,uBAAA;AAC3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,UAAA;AACZ,MAAA,iBAAA,GAAA,YAAA;AAAU,MAAA,uBAAA,EAAW;AAEjC,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,8DAAA;AAA4D,MAAA,uBAAA;AAC/D,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA,EAC+C,IAAA,UAAA;AACtD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAmB,MAAA,qBAAA,SAAA,SAAA,sDAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;AAClC,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACpB,MAAA,iBAAA,IAAA,WAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACW,EACV;;;IA3Bb;IACA;IAAY;IACZ;IAAe;IACf;IAAa;IACb;IAAa;IAAA;EAAA,GAAA,QAAA,CAAA,grCAAA,EAAA,CAAA;;;sEA2FJ,mBAAiB,CAAA;UAnG7B;uBACW,iBAAe,YACb,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;KAuBT,QAAA,CAAA,2qCAAA,EAAA,CAAA;;;;6EAkEU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,8DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}