import {
  AuthGuard
} from "./chunk-U3554LHH.js";
import "./chunk-EDH5VTX4.js";
import "./chunk-AKJJBQK4.js";
import {
  RouterModule
} from "./chunk-ESNMJU6B.js";
import "./chunk-RCHKY2RO.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-JTRMQXMJ.js";

// src/app/features/profile/profile.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-RKQ6G2ZS.js").then((c) => c.UserProfileComponent),
    canActivate: [AuthGuard]
  }
];
var ProfileModule = class _ProfileModule {
  static \u0275fac = function ProfileModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProfileModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _ProfileModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfileModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
export {
  ProfileModule
};
//# sourceMappingURL=chunk-QJ24L2VN.js.map
