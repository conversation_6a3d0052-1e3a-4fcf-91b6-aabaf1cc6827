import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Content,
  MatCardModule
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  <PERSON><PERSON>utton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/features/requests/components/requests-not-found/requests-not-found.component.ts
var RequestsNotFoundComponent = class _RequestsNotFoundComponent {
  constructor() {
  }
  static \u0275fac = function RequestsNotFoundComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestsNotFoundComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RequestsNotFoundComponent, selectors: [["app-requests-not-found"]], decls: 35, vars: 0, consts: [[1, "not-found-container"], [1, "not-found-card"], [1, "error-icon"], [1, "suggestions"], [1, "actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests"], ["mat-raised-button", "", "color", "accent", "routerLink", "/requests/new/leave"], ["mat-button", "", "routerLink", "/dashboard"]], template: function RequestsNotFoundComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-content")(3, "div", 2)(4, "mat-icon");
      \u0275\u0275text(5, "assignment_late");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "h1");
      \u0275\u0275text(7, "Request Not Found");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "p");
      \u0275\u0275text(9, "The request you're looking for doesn't exist or you don't have permission to view it.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 3)(11, "h3");
      \u0275\u0275text(12, "What you can do:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "ul")(14, "li");
      \u0275\u0275text(15, "Check if the request ID is correct");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "li");
      \u0275\u0275text(17, "Verify you have the necessary permissions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "li");
      \u0275\u0275text(19, "Browse your requests from the main list");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "li");
      \u0275\u0275text(21, "Create a new request if needed");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(22, "div", 4)(23, "button", 5)(24, "mat-icon");
      \u0275\u0275text(25, "list");
      \u0275\u0275elementEnd();
      \u0275\u0275text(26, " View All Requests ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "button", 6)(28, "mat-icon");
      \u0275\u0275text(29, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(30, " New Request ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(31, "button", 7)(32, "mat-icon");
      \u0275\u0275text(33, "home");
      \u0275\u0275elementEnd();
      \u0275\u0275text(34, " Dashboard ");
      \u0275\u0275elementEnd()()()()();
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    RouterLink,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatCardModule,
    MatCard,
    MatCardContent
  ], styles: ["\n\n.not-found-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card[_ngcontent-%COMP%] {\n  max-width: 600px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.error-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #ff6b6b;\n}\nh1[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 2rem;\n}\np[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n  font-size: 1.1rem;\n}\n.suggestions[_ngcontent-%COMP%] {\n  text-align: left;\n  margin: 2rem 0;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n}\n.suggestions[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  margin: 0;\n  padding-left: 1.5rem;\n}\n.suggestions[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n.actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .not-found-card[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=requests-not-found.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestsNotFoundComponent, [{
    type: Component,
    args: [{ selector: "app-requests-not-found", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule
    ], template: `
    <div class="not-found-container">
      <mat-card class="not-found-card">
        <mat-card-content>
          <div class="error-icon">
            <mat-icon>assignment_late</mat-icon>
          </div>
          <h1>Request Not Found</h1>
          <p>The request you're looking for doesn't exist or you don't have permission to view it.</p>
          <div class="suggestions">
            <h3>What you can do:</h3>
            <ul>
              <li>Check if the request ID is correct</li>
              <li>Verify you have the necessary permissions</li>
              <li>Browse your requests from the main list</li>
              <li>Create a new request if needed</li>
            </ul>
          </div>
          <div class="actions">
            <button mat-raised-button color="primary" routerLink="/requests">
              <mat-icon>list</mat-icon>
              View All Requests
            </button>
            <button mat-raised-button color="accent" routerLink="/requests/new/leave">
              <mat-icon>add</mat-icon>
              New Request
            </button>
            <button mat-button routerLink="/dashboard">
              <mat-icon>home</mat-icon>
              Dashboard
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;7be83edce995fcff8bb60a62896d921d1e6fe92e44a266bddaa2fff098aea3fb;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/requests/components/requests-not-found/requests-not-found.component.ts */\n.not-found-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 2rem;\n}\n.not-found-card {\n  max-width: 600px;\n  text-align: center;\n  padding: 2rem;\n}\n.error-icon {\n  margin-bottom: 1rem;\n}\n.error-icon mat-icon {\n  font-size: 64px;\n  width: 64px;\n  height: 64px;\n  color: #ff6b6b;\n}\nh1 {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 2rem;\n}\np {\n  color: #666;\n  margin-bottom: 1.5rem;\n  line-height: 1.6;\n  font-size: 1.1rem;\n}\n.suggestions {\n  text-align: left;\n  margin: 2rem 0;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.suggestions h3 {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.2rem;\n}\n.suggestions ul {\n  margin: 0;\n  padding-left: 1.5rem;\n}\n.suggestions li {\n  color: #666;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n.actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  flex-wrap: wrap;\n  margin-top: 2rem;\n}\n@media (max-width: 480px) {\n  .actions {\n    flex-direction: column;\n  }\n  .not-found-card {\n    padding: 1rem;\n  }\n  h1 {\n    font-size: 1.5rem;\n  }\n}\n/*# sourceMappingURL=requests-not-found.component.css.map */\n"] }]
  }], () => [], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RequestsNotFoundComponent, { className: "RequestsNotFoundComponent", filePath: "src/app/features/requests/components/requests-not-found/requests-not-found.component.ts", lineNumber: 142 });
})();
export {
  RequestsNotFoundComponent
};
//# sourceMappingURL=chunk-DLSF3URO.js.map
