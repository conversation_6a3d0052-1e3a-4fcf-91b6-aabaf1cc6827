{"version": 3, "sources": ["src/app/core/services/workflow.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { \n  WorkflowDto, \n  CreateWorkflowDto, \n  UpdateWorkflowDto,\n  PaginatedResponse,\n  PaginationParams\n} from '../models';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WorkflowService {\n  private readonly API_URL = `${environment.apiUrl}/api/Workflow`;\n\n  constructor(private http: HttpClient) {}\n\n  getWorkflows(params?: PaginationParams): Observable<PaginatedResponse<WorkflowDto>> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<PaginatedResponse<WorkflowDto>>(this.API_URL, { params: httpParams });\n  }\n\n  getActiveWorkflows(): Observable<WorkflowDto[]> {\n    return this.http.get<WorkflowDto[]>(`${this.API_URL}/active`);\n  }\n\n  getWorkflowById(id: string): Observable<WorkflowDto> {\n    return this.http.get<WorkflowDto>(`${this.API_URL}/${id}`);\n  }\n\n  getWorkflowWithSteps(id: string): Observable<WorkflowDto> {\n    return this.http.get<WorkflowDto>(`${this.API_URL}/${id}/steps`);\n  }\n\n  createWorkflow(workflow: CreateWorkflowDto): Observable<WorkflowDto> {\n    return this.http.post<WorkflowDto>(this.API_URL, workflow);\n  }\n\n  updateWorkflow(id: string, workflow: UpdateWorkflowDto): Observable<WorkflowDto> {\n    return this.http.put<WorkflowDto>(`${this.API_URL}/${id}`, workflow);\n  }\n\n  deleteWorkflow(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Get workflow steps\n  getWorkflowSteps(workflowId: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.API_URL}/${workflowId}/steps`);\n  }\n\n  activateWorkflow(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/activate`, {});\n  }\n\n  deactivateWorkflow(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/deactivate`, {});\n  }\n\n  duplicateWorkflow(id: string): Observable<WorkflowDto> {\n    return this.http.post<WorkflowDto>(`${this.API_URL}/${id}/duplicate`, {});\n  }\n\n  getWorkflowVersions(workflowName: string): Observable<WorkflowDto[]> {\n    return this.http.get<WorkflowDto[]>(`${this.API_URL}/versions/${workflowName}`);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAeM,IAAO,kBAAP,MAAO,iBAAe;EAGN;EAFH,UAAU,GAAG,YAAY,MAAM;EAEhD,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;EAEvC,aAAa,QAAyB;AACpC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,WAAW,SAAQ,CAAE;AAC7F,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAY,qBAAa,WAAW,IAAI,cAAc,OAAO,UAAU;AAClF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAe,qBAAa,WAAW,IAAI,iBAAiB,OAAO,aAAa;IAC7F;AAEA,WAAO,KAAK,KAAK,IAAoC,KAAK,SAAS,EAAE,QAAQ,WAAU,CAAE;EAC3F;EAEA,qBAAkB;AAChB,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,SAAS;EAC9D;EAEA,gBAAgB,IAAU;AACxB,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE;EAC3D;EAEA,qBAAqB,IAAU;AAC7B,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,OAAO,IAAI,EAAE,QAAQ;EACjE;EAEA,eAAe,UAA2B;AACxC,WAAO,KAAK,KAAK,KAAkB,KAAK,SAAS,QAAQ;EAC3D;EAEA,eAAe,IAAY,UAA2B;AACpD,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,QAAQ;EACrE;EAEA,eAAe,IAAU;AACvB,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE;EACvD;;EAGA,iBAAiB,YAAkB;AACjC,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,IAAI,UAAU,QAAQ;EACnE;EAEA,iBAAiB,IAAU;AACzB,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,aAAa,CAAA,CAAE;EACnE;EAEA,mBAAmB,IAAU;AAC3B,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,eAAe,CAAA,CAAE;EACrE;EAEA,kBAAkB,IAAU;AAC1B,WAAO,KAAK,KAAK,KAAkB,GAAG,KAAK,OAAO,IAAI,EAAE,cAAc,CAAA,CAAE;EAC1E;EAEA,oBAAoB,cAAoB;AACtC,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,aAAa,YAAY,EAAE;EAChF;;qCA9DW,kBAAe,mBAAA,UAAA,CAAA;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YAFd,OAAM,CAAA;;;sEAEP,iBAAe,CAAA;UAH3B;WAAW;MACV,YAAY;KACb;;;", "names": []}