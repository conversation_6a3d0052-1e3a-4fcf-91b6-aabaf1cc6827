import {
  MatDialogModule
} from "./chunk-DCUXBVDV.js";
import {
  WorkflowService
} from "./chunk-ILRPBHOG.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-43VBYOSE.js";
import {
  Mat<PERSON><PERSON><PERSON>,
  MatDividerModule
} from "./chunk-7XGPZDLL.js";
import "./chunk-XSFMJNUM.js";
import {
  MatCheckbox,
  MatCheckboxModule
} from "./chunk-GBVM2TU7.js";
import "./chunk-TCMHSQZD.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-DKEYCCVZ.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-3EIBYAGU.js";
import "./chunk-W6VU2H2S.js";
import {
  MatError,
  Mat<PERSON>orm<PERSON>ield,
  <PERSON><PERSON>abel
} from "./chunk-R3ISBMK2.js";
import {
  DefaultValueAccessor,
  FormArray,
  FormArrayName,
  FormBuilder,
  FormControlName,
  FormGroup,
  FormGroupDirective,
  FormGroupName,
  FormsModule,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NumberValueAccessor,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-D2SQUTFC.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-L52YVPND.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-5XTAYFTV.js";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  NgForOf,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/features/workflows/components/workflow-designer/workflow-designer.component.ts
function WorkflowDesignerComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDesignerComponent_div_2_mat_error_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Workflow name is required ");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDesignerComponent_div_2_div_40_mat_error_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Step name is required ");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDesignerComponent_div_2_div_40_mat_error_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Responsible role is required ");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDesignerComponent_div_2_div_40_mat_divider_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "mat-divider");
  }
}
function WorkflowDesignerComponent_div_2_div_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 22)(1, "div", 23)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 24);
    \u0275\u0275listener("click", function WorkflowDesignerComponent_div_2_div_40_Template_button_click_4_listener() {
      const i_r4 = \u0275\u0275restoreView(_r3).index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.removeStep(i_r4));
    });
    \u0275\u0275elementStart(5, "mat-icon");
    \u0275\u0275text(6, "delete");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(7, "div", 25)(8, "div", 6)(9, "mat-form-field", 10)(10, "mat-label");
    \u0275\u0275text(11, "Step Name");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "input", 26);
    \u0275\u0275template(13, WorkflowDesignerComponent_div_2_div_40_mat_error_13_Template, 2, 0, "mat-error", 2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "mat-form-field", 10)(15, "mat-label");
    \u0275\u0275text(16, "Order");
    \u0275\u0275elementEnd();
    \u0275\u0275element(17, "input", 27);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "div", 6)(19, "mat-form-field", 10)(20, "mat-label");
    \u0275\u0275text(21, "Responsible Role");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(22, "mat-select", 28)(23, "mat-option", 29);
    \u0275\u0275text(24, "Employee");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "mat-option", 30);
    \u0275\u0275text(26, "Manager");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "mat-option", 31);
    \u0275\u0275text(28, "HR");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "mat-option", 32);
    \u0275\u0275text(30, "Admin");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(31, WorkflowDesignerComponent_div_2_div_40_mat_error_31_Template, 2, 0, "mat-error", 2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(32, "mat-form-field", 10)(33, "mat-label");
    \u0275\u0275text(34, "Due in Hours (Optional)");
    \u0275\u0275elementEnd();
    \u0275\u0275element(35, "input", 33);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(36, WorkflowDesignerComponent_div_2_div_40_mat_divider_36_Template, 1, 0, "mat-divider", 2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_7_0;
    let tmp_9_0;
    const stepControl_r5 = ctx.$implicit;
    const i_r4 = ctx.index;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("formGroupName", i_r4);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Step ", i_r4 + 1, "");
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.stepsArray.length <= 1);
    \u0275\u0275advance(9);
    \u0275\u0275property("ngIf", (tmp_7_0 = stepControl_r5.get("stepName")) == null ? null : tmp_7_0.hasError("required"));
    \u0275\u0275advance(4);
    \u0275\u0275property("value", i_r4 + 1);
    \u0275\u0275advance(14);
    \u0275\u0275property("ngIf", (tmp_9_0 = stepControl_r5.get("responsibleRole")) == null ? null : tmp_9_0.hasError("required"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", i_r4 < ctx_r1.stepsArray.length - 1);
  }
}
function WorkflowDesignerComponent_div_2_div_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34)(1, "mat-icon");
    \u0275\u0275text(2, "info");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, 'No steps added yet. Click "Add Step" to create your first workflow step.');
    \u0275\u0275elementEnd()();
  }
}
function WorkflowDesignerComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div")(1, "form", 4);
    \u0275\u0275listener("ngSubmit", function WorkflowDesignerComponent_div_2_Template_form_ngSubmit_1_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onSubmit());
    });
    \u0275\u0275elementStart(2, "mat-card", 5)(3, "mat-card-header")(4, "mat-card-title")(5, "mat-icon");
    \u0275\u0275text(6, "settings");
    \u0275\u0275elementEnd();
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 6)(10, "mat-form-field", 7)(11, "mat-label");
    \u0275\u0275text(12, "Workflow Name");
    \u0275\u0275elementEnd();
    \u0275\u0275element(13, "input", 8);
    \u0275\u0275template(14, WorkflowDesignerComponent_div_2_mat_error_14_Template, 2, 0, "mat-error", 2);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 6)(16, "mat-form-field", 7)(17, "mat-label");
    \u0275\u0275text(18, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(19, "textarea", 9);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "div", 6)(21, "mat-form-field", 10)(22, "mat-label");
    \u0275\u0275text(23, "Version");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "input", 11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "mat-checkbox", 12);
    \u0275\u0275text(26, " Active Workflow ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(27, "mat-card", 13)(28, "mat-card-header")(29, "mat-card-title")(30, "mat-icon");
    \u0275\u0275text(31, "timeline");
    \u0275\u0275elementEnd();
    \u0275\u0275text(32, " Workflow Steps ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "div", 14)(34, "button", 15);
    \u0275\u0275listener("click", function WorkflowDesignerComponent_div_2_Template_button_click_34_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.addStep());
    });
    \u0275\u0275elementStart(35, "mat-icon");
    \u0275\u0275text(36, "add");
    \u0275\u0275elementEnd();
    \u0275\u0275text(37, " Add Step ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(38, "mat-card-content")(39, "div", 16);
    \u0275\u0275template(40, WorkflowDesignerComponent_div_2_div_40_Template, 37, 7, "div", 17)(41, WorkflowDesignerComponent_div_2_div_41_Template, 5, 0, "div", 18);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(42, "div", 19)(43, "button", 20)(44, "mat-icon");
    \u0275\u0275text(45, "cancel");
    \u0275\u0275elementEnd();
    \u0275\u0275text(46, " Cancel ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(47, "button", 21)(48, "mat-icon");
    \u0275\u0275text(49, "save");
    \u0275\u0275elementEnd();
    \u0275\u0275text(50);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    let tmp_3_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r1.workflowForm);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", ctx_r1.isEditMode ? "Edit Workflow" : "Create New Workflow", " ");
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r1.workflowForm.get("name")) == null ? null : tmp_3_0.hasError("required"));
    \u0275\u0275advance(26);
    \u0275\u0275property("ngForOf", ctx_r1.stepsArray.controls);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.stepsArray.length === 0);
    \u0275\u0275advance(6);
    \u0275\u0275property("disabled", ctx_r1.workflowForm.invalid || ctx_r1.isSubmitting);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.isEditMode ? "Update Workflow" : "Create Workflow", " ");
  }
}
var WorkflowDesignerComponent = class _WorkflowDesignerComponent {
  fb;
  route;
  router;
  workflowService;
  snackBar;
  destroy$ = new Subject();
  workflowForm;
  loading = false;
  isSubmitting = false;
  isEditMode = false;
  workflowId;
  constructor(fb, route, router, workflowService, snackBar) {
    this.fb = fb;
    this.route = route;
    this.router = router;
    this.workflowService = workflowService;
    this.snackBar = snackBar;
    this.initForm();
  }
  ngOnInit() {
    this.workflowId = this.route.snapshot.params["id"];
    this.isEditMode = !!this.workflowId;
    if (this.isEditMode) {
      this.loadWorkflow();
    } else {
      this.addStep();
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  get stepsArray() {
    return this.workflowForm.get("steps");
  }
  initForm() {
    this.workflowForm = this.fb.group({
      name: ["", Validators.required],
      description: [""],
      version: [1, [Validators.required, Validators.min(1)]],
      isActive: [true],
      steps: this.fb.array([])
    });
  }
  createStepFormGroup(step) {
    return this.fb.group({
      stepName: [step?.stepName || "", Validators.required],
      order: [step?.order || this.stepsArray.length + 1],
      responsibleRole: [step?.responsibleRole || "", Validators.required],
      dueInHours: [step?.dueInHours || null]
    });
  }
  addStep() {
    const stepGroup = this.createStepFormGroup();
    this.stepsArray.push(stepGroup);
    this.updateStepOrders();
  }
  removeStep(index) {
    if (this.stepsArray.length > 1) {
      this.stepsArray.removeAt(index);
      this.updateStepOrders();
    }
  }
  updateStepOrders() {
    this.stepsArray.controls.forEach((control, index) => {
      control.get("order")?.setValue(index + 1);
    });
  }
  loadWorkflow() {
    if (!this.workflowId)
      return;
    this.loading = true;
    this.workflowService.getWorkflowWithSteps(this.workflowId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (workflow) => {
        this.populateForm(workflow);
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading workflow:", error);
        this.snackBar.open("Error loading workflow", "Close", { duration: 3e3 });
        this.loading = false;
      }
    });
  }
  populateForm(workflow) {
    this.workflowForm.patchValue({
      name: workflow.name,
      description: workflow.description,
      version: workflow.version,
      isActive: workflow.isActive
    });
    while (this.stepsArray.length !== 0) {
      this.stepsArray.removeAt(0);
    }
    if (workflow.steps && workflow.steps.length > 0) {
      const sortedSteps = [...workflow.steps].sort((a, b) => a.order - b.order);
      sortedSteps.forEach((step) => {
        this.stepsArray.push(this.createStepFormGroup(step));
      });
    } else {
      this.addStep();
    }
  }
  onSubmit() {
    if (this.workflowForm.invalid) {
      this.markFormGroupTouched(this.workflowForm);
      return;
    }
    this.isSubmitting = true;
    const formValue = this.workflowForm.value;
    const steps = formValue.steps.map((step, index) => ({
      stepName: step.stepName,
      order: index + 1,
      responsibleRole: step.responsibleRole,
      dueInHours: step.dueInHours || void 0
    }));
    if (this.isEditMode && this.workflowId) {
      const updateData = {
        name: formValue.name,
        description: formValue.description,
        version: formValue.version,
        isActive: formValue.isActive
      };
      this.workflowService.updateWorkflow(this.workflowId, updateData).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.snackBar.open("Workflow updated successfully!", "Close", { duration: 3e3 });
          this.router.navigate(["/workflows"]);
        },
        error: (error) => {
          console.error("Error updating workflow:", error);
          this.snackBar.open("Error updating workflow", "Close", { duration: 3e3 });
          this.isSubmitting = false;
        }
      });
    } else {
      const createData = {
        name: formValue.name,
        description: formValue.description,
        version: formValue.version,
        isActive: formValue.isActive,
        steps
      };
      this.workflowService.createWorkflow(createData).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.snackBar.open("Workflow created successfully!", "Close", { duration: 3e3 });
          this.router.navigate(["/workflows"]);
        },
        error: (error) => {
          console.error("Error creating workflow:", error);
          this.snackBar.open("Error creating workflow", "Close", { duration: 3e3 });
          this.isSubmitting = false;
        }
      });
    }
  }
  markFormGroupTouched(formGroup) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach((arrayControl) => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          }
        });
      } else {
        control?.markAsTouched();
      }
    });
  }
  static \u0275fac = function WorkflowDesignerComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowDesignerComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(WorkflowService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowDesignerComponent, selectors: [["app-workflow-designer"]], decls: 3, vars: 2, consts: [[1, "workflow-designer-container"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], [1, "loading-container"], [3, "ngSubmit", "formGroup"], [1, "workflow-info-card"], [1, "form-row"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "name", "placeholder", "Enter workflow name"], ["matInput", "", "formControlName", "description", "rows", "3", "placeholder", "Describe this workflow"], ["appearance", "outline"], ["matInput", "", "type", "number", "formControlName", "version", "min", "1"], ["formControlName", "isActive"], [1, "workflow-steps-card"], [1, "header-actions"], ["type", "button", "mat-raised-button", "", "color", "accent", 3, "click"], ["formArrayName", "steps"], ["class", "step-item", 3, "formGroupName", 4, "ngFor", "ngForOf"], ["class", "no-steps", 4, "ngIf"], [1, "actions-section"], ["type", "button", "mat-button", "", "routerLink", "/workflows"], ["type", "submit", "mat-raised-button", "", "color", "primary", 3, "disabled"], [1, "step-item", 3, "formGroupName"], [1, "step-header"], ["type", "button", "mat-icon-button", "", "color", "warn", 3, "click", "disabled"], [1, "step-form"], ["matInput", "", "formControlName", "stepName", "placeholder", "Enter step name"], ["matInput", "", "type", "number", "formControlName", "order", "min", "1", "readonly", "", 3, "value"], ["formControlName", "responsibleRole"], ["value", "Employee"], ["value", "Manager"], ["value", "HR"], ["value", "Admin"], ["matInput", "", "type", "number", "formControlName", "dueInHours", "min", "1", "placeholder", "24"], [1, "no-steps"]], template: function WorkflowDesignerComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, WorkflowDesignerComponent_div_1_Template, 2, 0, "div", 1)(2, WorkflowDesignerComponent_div_2_Template, 51, 7, "div", 2);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    RouterModule,
    RouterLink,
    FormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NumberValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    MinValidator,
    ReactiveFormsModule,
    FormGroupDirective,
    FormControlName,
    FormGroupName,
    FormArrayName,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatError,
    MatInputModule,
    MatInput,
    MatSelectModule,
    MatSelect,
    MatOption,
    MatCheckboxModule,
    MatCheckbox,
    MatDividerModule,
    MatDivider,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatSnackBarModule,
    MatDialogModule
  ], styles: ["\n\n.workflow-designer-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.workflow-info-card[_ngcontent-%COMP%], \n.workflow-steps-card[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: flex-start;\n}\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.step-item[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.step-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.step-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n}\n.step-form[_ngcontent-%COMP%] {\n  background-color: white;\n  padding: 1rem;\n  border-radius: 4px;\n}\n.no-steps[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.no-steps[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n  margin-bottom: 0.5rem;\n  color: #ff9800;\n}\n.actions-section[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  padding: 1rem;\n  background-color: #f5f5f5;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n@media (max-width: 768px) {\n  .form-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .actions-section[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=workflow-designer.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowDesignerComponent, [{
    type: Component,
    args: [{ selector: "app-workflow-designer", standalone: true, imports: [
      CommonModule,
      RouterModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatCheckboxModule,
      MatDividerModule,
      MatProgressSpinnerModule,
      MatSnackBarModule,
      MatDialogModule
    ], template: `
    <div class="workflow-designer-container">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <!-- Designer Form -->
      <div *ngIf="!loading">
        <form [formGroup]="workflowForm" (ngSubmit)="onSubmit()">
          <!-- Workflow Basic Info -->
          <mat-card class="workflow-info-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>settings</mat-icon>
                {{isEditMode ? 'Edit Workflow' : 'Create New Workflow'}}
              </mat-card-title>
            </mat-card-header>

            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Workflow Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter workflow name">
                  <mat-error *ngIf="workflowForm.get('name')?.hasError('required')">
                    Workflow name is required
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3" placeholder="Describe this workflow"></textarea>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Version</mat-label>
                  <input matInput type="number" formControlName="version" min="1">
                </mat-form-field>

                <mat-checkbox formControlName="isActive">
                  Active Workflow
                </mat-checkbox>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Workflow Steps -->
          <mat-card class="workflow-steps-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>timeline</mat-icon>
                Workflow Steps
              </mat-card-title>
              <div class="header-actions">
                <button type="button" mat-raised-button color="accent" (click)="addStep()">
                  <mat-icon>add</mat-icon>
                  Add Step
                </button>
              </div>
            </mat-card-header>

            <mat-card-content>
              <div formArrayName="steps">
                <div *ngFor="let stepControl of stepsArray.controls; let i = index" 
                     [formGroupName]="i" 
                     class="step-item">
                  <div class="step-header">
                    <h4>Step {{i + 1}}</h4>
                    <button type="button" mat-icon-button color="warn" (click)="removeStep(i)" 
                            [disabled]="stepsArray.length <= 1">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>

                  <div class="step-form">
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Step Name</mat-label>
                        <input matInput formControlName="stepName" placeholder="Enter step name">
                        <mat-error *ngIf="stepControl.get('stepName')?.hasError('required')">
                          Step name is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Order</mat-label>
                        <input matInput type="number" formControlName="order" min="1" [value]="i + 1" readonly>
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Responsible Role</mat-label>
                        <mat-select formControlName="responsibleRole">
                          <mat-option value="Employee">Employee</mat-option>
                          <mat-option value="Manager">Manager</mat-option>
                          <mat-option value="HR">HR</mat-option>
                          <mat-option value="Admin">Admin</mat-option>
                        </mat-select>
                        <mat-error *ngIf="stepControl.get('responsibleRole')?.hasError('required')">
                          Responsible role is required
                        </mat-error>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Due in Hours (Optional)</mat-label>
                        <input matInput type="number" formControlName="dueInHours" min="1" placeholder="24">
                      </mat-form-field>
                    </div>
                  </div>

                  <mat-divider *ngIf="i < stepsArray.length - 1"></mat-divider>
                </div>

                <div *ngIf="stepsArray.length === 0" class="no-steps">
                  <mat-icon>info</mat-icon>
                  <p>No steps added yet. Click "Add Step" to create your first workflow step.</p>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Actions -->
          <div class="actions-section">
            <button type="button" mat-button routerLink="/workflows">
              <mat-icon>cancel</mat-icon>
              Cancel
            </button>
            <button type="submit" mat-raised-button color="primary" [disabled]="workflowForm.invalid || isSubmitting">
              <mat-icon>save</mat-icon>
              {{isEditMode ? 'Update Workflow' : 'Create Workflow'}}
            </button>
          </div>
        </form>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;6d74039ec412e71f2c46d14b5a32bcaaf924e9275cae747bc443aa38fd5d1423;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/workflows/components/workflow-designer/workflow-designer.component.ts */\n.workflow-designer-container {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.workflow-info-card,\n.workflow-steps-card {\n  margin-bottom: 1rem;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.form-row {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: flex-start;\n}\n.full-width {\n  width: 100%;\n}\n.step-item {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  background-color: #fafafa;\n}\n.step-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.step-header h4 {\n  margin: 0;\n  color: #333;\n}\n.step-form {\n  background-color: white;\n  padding: 1rem;\n  border-radius: 4px;\n}\n.no-steps {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.no-steps mat-icon {\n  font-size: 2rem;\n  width: 2rem;\n  height: 2rem;\n  margin-bottom: 0.5rem;\n  color: #ff9800;\n}\n.actions-section {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  padding: 1rem;\n  background-color: #f5f5f5;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n@media (max-width: 768px) {\n  .form-row {\n    flex-direction: column;\n  }\n  .actions-section {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=workflow-designer.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ActivatedRoute }, { type: Router }, { type: WorkflowService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowDesignerComponent, { className: "WorkflowDesignerComponent", filePath: "src/app/features/workflows/components/workflow-designer/workflow-designer.component.ts", lineNumber: 285 });
})();
export {
  WorkflowDesignerComponent
};
//# sourceMappingURL=chunk-UPBAHMRP.js.map
