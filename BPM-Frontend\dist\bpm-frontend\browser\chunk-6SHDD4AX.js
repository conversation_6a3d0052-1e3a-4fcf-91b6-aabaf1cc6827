import {
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-JTRMQXMJ.js";

// src/app/features/workflows/workflows-routing.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-LTMFCGD5.js").then((c) => c.WorkflowListComponent)
  },
  {
    path: "designer",
    loadComponent: () => import("./chunk-UPBAHMRP.js").then((c) => c.WorkflowDesignerComponent)
  },
  {
    path: "details/:id",
    loadComponent: () => import("./chunk-M66U36SH.js").then((c) => c.WorkflowDetailsComponent)
  },
  {
    path: "**",
    loadComponent: () => import("./chunk-XGSCCNX2.js").then((c) => c.WorkflowsNotFoundComponent)
  }
];
var WorkflowsRoutingModule = class _WorkflowsRoutingModule {
  static \u0275fac = function WorkflowsRoutingModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowsRoutingModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _WorkflowsRoutingModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowsRoutingModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();

// src/app/features/workflows/workflows.module.ts
var WorkflowsModule = class _WorkflowsModule {
  static \u0275fac = function WorkflowsModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowsModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _WorkflowsModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [
    CommonModule,
    RouterModule,
    WorkflowsRoutingModule
  ] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowsModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [
        CommonModule,
        RouterModule,
        WorkflowsRoutingModule
      ]
    }]
  }], null, null);
})();
export {
  WorkflowsModule
};
//# sourceMappingURL=chunk-6SHDD4AX.js.map
