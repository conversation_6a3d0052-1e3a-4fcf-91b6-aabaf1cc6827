import {
  RouterModule
} from "./chunk-ESNMJU6B.js";
import "./chunk-RCHKY2RO.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-JTRMQXMJ.js";

// src/app/features/dashboard/dashboard.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-LLXPY6UV.js").then((c) => c.DashboardComponent)
  },
  {
    path: "employee",
    loadComponent: () => import("./chunk-F2KQKJHG.js").then((c) => c.EmployeeDashboardComponent)
  },
  {
    path: "manager",
    loadComponent: () => import("./chunk-GI6W42A4.js").then((c) => c.ManagerDashboardComponent),
    data: { roles: ["Manager", "Admin"] }
  },
  {
    path: "hr",
    loadComponent: () => import("./chunk-RDGE6BLW.js").then((c) => c.HRDashboardComponent),
    data: { roles: ["HR", "Admin"] }
  },
  {
    path: "reports",
    loadComponent: () => import("./chunk-3NQSEHAD.js").then((c) => c.ReportingDashboardComponent),
    data: { roles: ["Manager", "HR", "Admin"] }
  },
  {
    path: "reporting",
    loadComponent: () => import("./chunk-3NQSEHAD.js").then((c) => c.ReportingDashboardComponent),
    data: { roles: ["Manager", "HR", "Admin"] }
  }
];
var DashboardModule = class _DashboardModule {
  static \u0275fac = function DashboardModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _DashboardModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();
export {
  DashboardModule
};
//# sourceMappingURL=chunk-L3VFCXBM.js.map
