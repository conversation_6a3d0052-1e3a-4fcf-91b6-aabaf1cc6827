{"version": 3, "sources": ["src/app/shared/components/unauthorized/unauthorized.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\n\n@Component({\n  selector: 'app-unauthorized',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule\n  ],\n  template: `\n    <div class=\"unauthorized-container\">\n      <mat-card class=\"unauthorized-card\">\n        <mat-card-content>\n          <div class=\"error-icon\">\n            <mat-icon>block</mat-icon>\n          </div>\n          <h1>Access Denied</h1>\n          <p>You don't have permission to access this page.</p>\n          <p>Please contact your administrator if you believe this is an error.</p>\n          <div class=\"actions\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/dashboard\">\n              <mat-icon>home</mat-icon>\n              Go to Dashboard\n            </button>\n            <button mat-button routerLink=\"/auth/login\">\n              <mat-icon>login</mat-icon>\n              Login as Different User\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .unauthorized-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .unauthorized-card {\n      max-width: 500px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #f44336;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1rem;\n      line-height: 1.6;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .actions {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class UnauthorizedComponent { }"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FM,IAAO,wBAAP,MAAO,uBAAqB;;qCAArB,wBAAqB;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,YAAA,GAAA,CAAA,cAAA,IAAA,cAAA,aAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA5E9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAoC,GAAA,YAAA,CAAA,EACE,GAAA,kBAAA,EAChB,GAAA,OAAA,CAAA,EACQ,GAAA,UAAA;AACZ,MAAA,iBAAA,GAAA,OAAA;AAAK,MAAA,uBAAA,EAAW;AAE5B,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,gDAAA;AAA8C,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,oEAAA;AAAkE,MAAA,uBAAA;AACrE,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA,EAC+C,IAAA,UAAA;AACtD,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA4C,IAAA,UAAA;AAChC,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACf,MAAA,iBAAA,IAAA,2BAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACW,EACV;;;IA3Bb;IACA;IAAY;IACZ;IAAe;IACf;IAAa;IACb;IAAa;IAAA;EAAA,GAAA,QAAA,CAAA,g/BAAA,EAAA,CAAA;;;sEA+EJ,uBAAqB,CAAA;UAvFjC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;KAuBT,QAAA,CAAA,uhCAAA,EAAA,CAAA;;;;6EAsDU,uBAAqB,EAAA,WAAA,yBAAA,UAAA,oEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}