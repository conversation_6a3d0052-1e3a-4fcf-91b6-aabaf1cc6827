import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-3EIBYAGU.js";
import "./chunk-W6VU2H2S.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-WTRFNDWH.js";
import {
  RequestType
} from "./chunk-XJ5TS5V6.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  NumberValueAccessor,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-D2SQUTFC.js";
import "./chunk-YJIBYHOF.js";
import "./chunk-AKJJBQK4.js";
import "./chunk-5XTAYFTV.js";
import {
  ActivatedRoute,
  Router
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  NgI<PERSON>,
  Ng<PERSON><PERSON>,
  NgSwitchCase,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TitleCasePip<PERSON>
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/features/requests/components/request-form/request-form.component.ts
function RequestFormComponent_ng_template_6_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Start Date is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_6_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " End Date is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_6_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Reason is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "label", 11);
    \u0275\u0275text(2, "Start Date:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "input", 12);
    \u0275\u0275template(4, RequestFormComponent_ng_template_6_div_4_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 10)(6, "label", 14);
    \u0275\u0275text(7, "End Date:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "input", 15);
    \u0275\u0275template(9, RequestFormComponent_ng_template_6_div_9_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 10)(11, "label", 16);
    \u0275\u0275text(12, "Reason:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(13, "textarea", 17);
    \u0275\u0275template(14, RequestFormComponent_ng_template_6_div_14_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ((tmp_1_0 = ctx_r0.requestForm.get("startDate")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.requestForm.get("startDate")) == null ? null : tmp_1_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_2_0 = ctx_r0.requestForm.get("endDate")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.requestForm.get("endDate")) == null ? null : tmp_2_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.requestForm.get("reason")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.requestForm.get("reason")) == null ? null : tmp_3_0.touched));
  }
}
function RequestFormComponent_ng_template_7_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Amount is required and must be a positive number. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_7_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Description is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "label", 19);
    \u0275\u0275text(2, "Amount:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "input", 20);
    \u0275\u0275template(4, RequestFormComponent_ng_template_7_div_4_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 10)(6, "label", 21);
    \u0275\u0275text(7, "Description:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "textarea", 22);
    \u0275\u0275template(9, RequestFormComponent_ng_template_7_div_9_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 10)(11, "label", 23);
    \u0275\u0275text(12, "Receipt:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(13, "input", 24);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ((tmp_1_0 = ctx_r0.requestForm.get("amount")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.requestForm.get("amount")) == null ? null : tmp_1_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_2_0 = ctx_r0.requestForm.get("description")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.requestForm.get("description")) == null ? null : tmp_2_0.touched));
  }
}
function RequestFormComponent_ng_template_8_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Course Name is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_8_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Provider is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_8_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Start Date is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "label", 25);
    \u0275\u0275text(2, "Course Name:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "input", 26);
    \u0275\u0275template(4, RequestFormComponent_ng_template_8_div_4_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 10)(6, "label", 27);
    \u0275\u0275text(7, "Provider:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "input", 28);
    \u0275\u0275template(9, RequestFormComponent_ng_template_8_div_9_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 10)(11, "label", 29);
    \u0275\u0275text(12, "Start Date:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(13, "input", 30);
    \u0275\u0275template(14, RequestFormComponent_ng_template_8_div_14_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ((tmp_1_0 = ctx_r0.requestForm.get("courseName")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.requestForm.get("courseName")) == null ? null : tmp_1_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_2_0 = ctx_r0.requestForm.get("provider")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.requestForm.get("provider")) == null ? null : tmp_2_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.requestForm.get("startDate")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.requestForm.get("startDate")) == null ? null : tmp_3_0.touched));
  }
}
function RequestFormComponent_ng_template_9_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Issue description is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_9_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Priority is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "label", 31);
    \u0275\u0275text(2, "Issue:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "textarea", 32);
    \u0275\u0275template(4, RequestFormComponent_ng_template_9_div_4_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 10)(6, "label", 33);
    \u0275\u0275text(7, "Priority:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "select", 34)(9, "option", 35);
    \u0275\u0275text(10, "--Select--");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "option", 36);
    \u0275\u0275text(12, "Low");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "option", 37);
    \u0275\u0275text(14, "Medium");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "option", 38);
    \u0275\u0275text(16, "High");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(17, RequestFormComponent_ng_template_9_div_17_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ((tmp_1_0 = ctx_r0.requestForm.get("issue")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.requestForm.get("issue")) == null ? null : tmp_1_0.touched));
    \u0275\u0275advance(13);
    \u0275\u0275property("ngIf", ((tmp_2_0 = ctx_r0.requestForm.get("priority")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.requestForm.get("priority")) == null ? null : tmp_2_0.touched));
  }
}
function RequestFormComponent_ng_template_10_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " Field to update is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_10_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18);
    \u0275\u0275text(1, " New value is required. ");
    \u0275\u0275elementEnd();
  }
}
function RequestFormComponent_ng_template_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "label", 39);
    \u0275\u0275text(2, "Field to Update:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "input", 40);
    \u0275\u0275template(4, RequestFormComponent_ng_template_10_div_4_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 10)(6, "label", 41);
    \u0275\u0275text(7, "New Value:");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "input", 42);
    \u0275\u0275template(9, RequestFormComponent_ng_template_10_div_9_Template, 2, 0, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ((tmp_1_0 = ctx_r0.requestForm.get("fieldToUpdate")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.requestForm.get("fieldToUpdate")) == null ? null : tmp_1_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_2_0 = ctx_r0.requestForm.get("newValue")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.requestForm.get("newValue")) == null ? null : tmp_2_0.touched));
  }
}
function RequestFormComponent_ng_template_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1, "Please select a request type.");
    \u0275\u0275elementEnd();
  }
}
var RequestFormComponent = class _RequestFormComponent {
  fb;
  route;
  router;
  requestService;
  snackBar;
  destroy$ = new Subject();
  requestForm;
  requestType = "";
  isSubmitting = false;
  constructor(fb, route, router, requestService, snackBar) {
    this.fb = fb;
    this.route = route;
    this.router = router;
    this.requestService = requestService;
    this.snackBar = snackBar;
  }
  ngOnInit() {
    this.route.paramMap.subscribe((params) => {
      this.requestType = params.get("type") || "";
      this.initForm();
    });
  }
  initForm() {
    switch (this.requestType) {
      case "leave":
        this.requestForm = this.fb.group({
          startDate: ["", Validators.required],
          endDate: ["", Validators.required],
          reason: ["", Validators.required]
        });
        break;
      case "expense":
        this.requestForm = this.fb.group({
          amount: ["", [Validators.required, Validators.min(0)]],
          description: ["", Validators.required],
          receipt: [null]
        });
        break;
      case "training":
        this.requestForm = this.fb.group({
          courseName: ["", Validators.required],
          provider: ["", Validators.required],
          startDate: ["", Validators.required]
        });
        break;
      case "it-ticket":
        this.requestForm = this.fb.group({
          issue: ["", Validators.required],
          priority: ["", Validators.required]
        });
        break;
      case "profile-update":
        this.requestForm = this.fb.group({
          fieldToUpdate: ["", Validators.required],
          newValue: ["", Validators.required]
        });
        break;
      default:
        this.requestForm = this.fb.group({});
        console.warn(`Unknown request type: ${this.requestType}`);
        break;
    }
  }
  onSubmit() {
    if (this.requestForm.valid) {
      this.isSubmitting = true;
      const requestData = {
        type: this.getRequestTypeEnum(this.requestType),
        title: this.generateTitle(),
        description: this.generateDescription(),
        workflowId: this.getDefaultWorkflowId()
      };
      this.requestService.createRequest(requestData).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response) => {
          this.snackBar.open("Request submitted successfully!", "Close", { duration: 3e3 });
          this.router.navigate(["/requests"]);
        },
        error: (error) => {
          console.error("Error submitting request:", error);
          this.snackBar.open("Error submitting request. Please try again.", "Close", { duration: 3e3 });
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.requestForm);
    }
  }
  getRequestTypeEnum(type) {
    switch (type) {
      case "leave":
        return RequestType.Leave;
      case "expense":
        return RequestType.Expense;
      case "training":
        return RequestType.Training;
      case "it-ticket":
        return RequestType.ITSupport;
      case "profile-update":
        return RequestType.ProfileUpdate;
      default:
        return RequestType.Leave;
    }
  }
  generateTitle() {
    const formValue = this.requestForm.value;
    switch (this.requestType) {
      case "leave":
        return `Leave Request: ${formValue.startDate} to ${formValue.endDate}`;
      case "expense":
        return `Expense Report: $${formValue.amount}`;
      case "training":
        return `Training Request: ${formValue.courseName}`;
      case "it-ticket":
        return `IT Support: ${formValue.issue}`;
      case "profile-update":
        return `Profile Update: ${formValue.fieldToUpdate}`;
      default:
        return "New Request";
    }
  }
  generateDescription() {
    const formValue = this.requestForm.value;
    switch (this.requestType) {
      case "leave":
        return `Leave request from ${formValue.startDate} to ${formValue.endDate}. Reason: ${formValue.reason}`;
      case "expense":
        return `Expense report for $${formValue.amount}. Description: ${formValue.description}`;
      case "training":
        return `Training request for ${formValue.courseName} by ${formValue.provider}, starting ${formValue.startDate}`;
      case "it-ticket":
        return `IT Support ticket: ${formValue.issue}. Priority: ${formValue.priority}`;
      case "profile-update":
        return `Profile update request to change ${formValue.fieldToUpdate} to ${formValue.newValue}`;
      default:
        return JSON.stringify(formValue);
    }
  }
  getDefaultWorkflowId() {
    return "00000000-0000-0000-0000-000000000000";
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  markFormGroupTouched(formGroup) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
  static \u0275fac = function RequestFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(RequestService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RequestFormComponent, selectors: [["app-request-form"]], decls: 14, vars: 6, consts: [[1, "request-form-container"], [3, "ngSubmit", "formGroup"], [3, "ngSwitch"], ["ngSwitchCase", "leave"], ["ngSwitchCase", "expense"], ["ngSwitchCase", "training"], ["ngSwitchCase", "it-ticket"], ["ngSwitchCase", "profile-update"], ["ngSwitchDefault", ""], ["type", "submit", 3, "disabled"], [1, "form-group"], ["for", "startDate"], ["type", "date", "id", "startDate", "formControlName", "startDate"], ["class", "error-message", 4, "ngIf"], ["for", "endDate"], ["type", "date", "id", "endDate", "formControlName", "endDate"], ["for", "reason"], ["id", "reason", "formControlName", "reason"], [1, "error-message"], ["for", "amount"], ["type", "number", "id", "amount", "formControlName", "amount"], ["for", "description"], ["id", "description", "formControlName", "description"], ["for", "receipt"], ["type", "file", "id", "receipt", "formControlName", "receipt"], ["for", "courseName"], ["type", "text", "id", "courseName", "formControlName", "courseName"], ["for", "provider"], ["type", "text", "id", "provider", "formControlName", "provider"], ["for", "trainingStartDate"], ["type", "date", "id", "trainingStartDate", "formControlName", "startDate"], ["for", "issue"], ["id", "issue", "formControlName", "issue"], ["for", "priority"], ["id", "priority", "formControlName", "priority"], ["value", ""], ["value", "low"], ["value", "medium"], ["value", "high"], ["for", "fieldToUpdate"], ["type", "text", "id", "fieldToUpdate", "formControlName", "fieldToUpdate"], ["for", "newValue"], ["type", "text", "id", "newValue", "formControlName", "newValue"]], template: function RequestFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h2");
      \u0275\u0275text(2);
      \u0275\u0275pipe(3, "titlecase");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "form", 1);
      \u0275\u0275listener("ngSubmit", function RequestFormComponent_Template_form_ngSubmit_4_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementContainerStart(5, 2);
      \u0275\u0275template(6, RequestFormComponent_ng_template_6_Template, 15, 3, "ng-template", 3)(7, RequestFormComponent_ng_template_7_Template, 14, 2, "ng-template", 4)(8, RequestFormComponent_ng_template_8_Template, 15, 3, "ng-template", 5)(9, RequestFormComponent_ng_template_9_Template, 18, 2, "ng-template", 6)(10, RequestFormComponent_ng_template_10_Template, 10, 2, "ng-template", 7)(11, RequestFormComponent_ng_template_11_Template, 2, 0, "ng-template", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementStart(12, "button", 9);
      \u0275\u0275text(13, " Submit Request ");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("Submit ", \u0275\u0275pipeBind1(3, 4, ctx.requestType), " Request");
      \u0275\u0275advance(2);
      \u0275\u0275property("formGroup", ctx.requestForm);
      \u0275\u0275advance();
      \u0275\u0275property("ngSwitch", ctx.requestType);
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", ctx.requestForm.invalid);
    }
  }, dependencies: [CommonModule, NgIf, NgSwitch, NgSwitchCase, NgSwitchDefault, TitleCasePipe, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, MatSnackBarModule], styles: ["\n\n.request-form-container[_ngcontent-%COMP%] {\n  max-width: 600px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.request-form-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #333;\n  margin-bottom: 20px;\n}\n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\n  margin-bottom: 15px;\n}\n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n  color: #555;\n}\n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[type=date][_ngcontent-%COMP%], \n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%], \n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  box-sizing: border-box;\n}\n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\n  resize: vertical;\n  min-height: 80px;\n}\n.request-form-container[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\n  color: #dc3545;\n  font-size: 0.875em;\n  margin-top: 5px;\n}\n.request-form-container[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%] {\n  display: block;\n  width: 100%;\n  padding: 10px;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n.request-form-container[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]:disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n.request-form-container[_ngcontent-%COMP%]   button[type=submit][_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n/*# sourceMappingURL=request-form.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestFormComponent, [{
    type: Component,
    args: [{ selector: "app-request-form", standalone: true, imports: [CommonModule, ReactiveFormsModule, MatSnackBarModule], template: `<div class="request-form-container">
  <h2>Submit {{ requestType | titlecase }} Request</h2>

  <form [formGroup]="requestForm" (ngSubmit)="onSubmit()">
    <ng-container [ngSwitch]="requestType">
      <ng-template ngSwitchCase="leave">
        <div class="form-group">
          <label for="startDate">Start Date:</label>
          <input type="date" id="startDate" formControlName="startDate" />
          <div
            *ngIf="
              requestForm.get('startDate')?.invalid &&
              requestForm.get('startDate')?.touched
            "
            class="error-message"
          >
            Start Date is required.
          </div>
        </div>
        <div class="form-group">
          <label for="endDate">End Date:</label>
          <input type="date" id="endDate" formControlName="endDate" />
          <div
            *ngIf="
              requestForm.get('endDate')?.invalid &&
              requestForm.get('endDate')?.touched
            "
            class="error-message"
          >
            End Date is required.
          </div>
        </div>
        <div class="form-group">
          <label for="reason">Reason:</label>
          <textarea id="reason" formControlName="reason"></textarea>
          <div
            *ngIf="
              requestForm.get('reason')?.invalid &&
              requestForm.get('reason')?.touched
            "
            class="error-message"
          >
            Reason is required.
          </div>
        </div>
      </ng-template>

      <ng-template ngSwitchCase="expense">
        <div class="form-group">
          <label for="amount">Amount:</label>
          <input type="number" id="amount" formControlName="amount" />
          <div
            *ngIf="
              requestForm.get('amount')?.invalid &&
              requestForm.get('amount')?.touched
            "
            class="error-message"
          >
            Amount is required and must be a positive number.
          </div>
        </div>
        <div class="form-group">
          <label for="description">Description:</label>
          <textarea id="description" formControlName="description"></textarea>
          <div
            *ngIf="
              requestForm.get('description')?.invalid &&
              requestForm.get('description')?.touched
            "
            class="error-message"
          >
            Description is required.
          </div>
        </div>
        <div class="form-group">
          <label for="receipt">Receipt:</label>
          <input type="file" id="receipt" formControlName="receipt" />
        </div>
      </ng-template>

      <ng-template ngSwitchCase="training">
        <div class="form-group">
          <label for="courseName">Course Name:</label>
          <input type="text" id="courseName" formControlName="courseName" />
          <div
            *ngIf="
              requestForm.get('courseName')?.invalid &&
              requestForm.get('courseName')?.touched
            "
            class="error-message"
          >
            Course Name is required.
          </div>
        </div>
        <div class="form-group">
          <label for="provider">Provider:</label>
          <input type="text" id="provider" formControlName="provider" />
          <div
            *ngIf="
              requestForm.get('provider')?.invalid &&
              requestForm.get('provider')?.touched
            "
            class="error-message"
          >
            Provider is required.
          </div>
        </div>
        <div class="form-group">
          <label for="trainingStartDate">Start Date:</label>
          <input
            type="date"
            id="trainingStartDate"
            formControlName="startDate"
          />
          <div
            *ngIf="
              requestForm.get('startDate')?.invalid &&
              requestForm.get('startDate')?.touched
            "
            class="error-message"
          >
            Start Date is required.
          </div>
        </div>
      </ng-template>

      <ng-template ngSwitchCase="it-ticket">
        <div class="form-group">
          <label for="issue">Issue:</label>
          <textarea id="issue" formControlName="issue"></textarea>
          <div
            *ngIf="
              requestForm.get('issue')?.invalid &&
              requestForm.get('issue')?.touched
            "
            class="error-message"
          >
            Issue description is required.
          </div>
        </div>
        <div class="form-group">
          <label for="priority">Priority:</label>
          <select id="priority" formControlName="priority">
            <option value="">--Select--</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
          </select>
          <div
            *ngIf="
              requestForm.get('priority')?.invalid &&
              requestForm.get('priority')?.touched
            "
            class="error-message"
          >
            Priority is required.
          </div>
        </div>
      </ng-template>

      <ng-template ngSwitchCase="profile-update">
        <div class="form-group">
          <label for="fieldToUpdate">Field to Update:</label>
          <input
            type="text"
            id="fieldToUpdate"
            formControlName="fieldToUpdate"
          />
          <div
            *ngIf="
              requestForm.get('fieldToUpdate')?.invalid &&
              requestForm.get('fieldToUpdate')?.touched
            "
            class="error-message"
          >
            Field to update is required.
          </div>
        </div>
        <div class="form-group">
          <label for="newValue">New Value:</label>
          <input type="text" id="newValue" formControlName="newValue" />
          <div
            *ngIf="
              requestForm.get('newValue')?.invalid &&
              requestForm.get('newValue')?.touched
            "
            class="error-message"
          >
            New value is required.
          </div>
        </div>
      </ng-template>

      <ng-template ngSwitchDefault>
        <p>Please select a request type.</p>
      </ng-template>
    </ng-container>

    <button type="submit" [disabled]="requestForm.invalid">
      Submit Request
    </button>
  </form>
</div>`, styles: ["/* src/app/features/requests/components/request-form/request-form.component.scss */\n.request-form-container {\n  max-width: 600px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.request-form-container h2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 20px;\n}\n.request-form-container .form-group {\n  margin-bottom: 15px;\n}\n.request-form-container .form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n  color: #555;\n}\n.request-form-container .form-group input[type=text],\n.request-form-container .form-group input[type=number],\n.request-form-container .form-group input[type=date],\n.request-form-container .form-group textarea,\n.request-form-container .form-group select {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ccc;\n  border-radius: 4px;\n  box-sizing: border-box;\n}\n.request-form-container .form-group textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n.request-form-container .form-group .error-message {\n  color: #dc3545;\n  font-size: 0.875em;\n  margin-top: 5px;\n}\n.request-form-container button[type=submit] {\n  display: block;\n  width: 100%;\n  padding: 10px;\n  background-color: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 16px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n.request-form-container button[type=submit]:disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n.request-form-container button[type=submit]:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n/*# sourceMappingURL=request-form.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ActivatedRoute }, { type: Router }, { type: RequestService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RequestFormComponent, { className: "RequestFormComponent", filePath: "src/app/features/requests/components/request-form/request-form.component.ts", lineNumber: 18 });
})();
export {
  RequestFormComponent
};
//# sourceMappingURL=chunk-DQ4S732Z.js.map
