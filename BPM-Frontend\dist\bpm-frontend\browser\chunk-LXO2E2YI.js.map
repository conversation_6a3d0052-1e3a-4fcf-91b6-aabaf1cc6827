{"version": 3, "sources": ["src/app/features/admin/components/user-management/user-management.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatPaginatorModule, PageEvent } from '@angular/material/paginator';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';\n\nimport { UserDto, PaginationParams, PaginatedResponse } from '../../../../core/models';\nimport { UserService } from '../../../../core/services/user.service';\n\n@Component({\n  selector: 'app-user-management',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatTableModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule,\n    MatChipsModule,\n    MatPaginatorModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatProgressSpinnerModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatTooltipModule\n  ],\n  template: `\n    <div class=\"user-management-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>people</mat-icon>\n            User Management\n          </mat-card-title>\n          <div class=\"header-actions\">\n            <button mat-raised-button color=\"primary\" (click)=\"openCreateUserDialog()\">\n              <mat-icon>person_add</mat-icon>\n              Add User\n            </button>\n          </div>\n        </mat-card-header>\n\n        <mat-card-content>\n          <!-- Filters -->\n          <div class=\"filters\">\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Search</mat-label>\n              <input matInput [(ngModel)]=\"searchTerm\" (ngModelChange)=\"onSearchChange()\" placeholder=\"Search users...\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\">\n              <mat-label>Role</mat-label>\n              <mat-select [(ngModel)]=\"selectedRole\" (selectionChange)=\"onFilterChange()\">\n                <mat-option value=\"\">All Roles</mat-option>\n                <mat-option value=\"Employee\">Employee</mat-option>\n                <mat-option value=\"Manager\">Manager</mat-option>\n                <mat-option value=\"HR\">HR</mat-option>\n                <mat-option value=\"Admin\">Admin</mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n\n          <!-- Loading Spinner -->\n          <div *ngIf=\"loading\" class=\"loading-container\">\n            <mat-spinner></mat-spinner>\n          </div>\n\n          <!-- Users Table -->\n          <div *ngIf=\"!loading\" class=\"table-container\">\n            <table mat-table [dataSource]=\"users\" class=\"users-table\">\n              <!-- Avatar Column -->\n              <ng-container matColumnDef=\"avatar\">\n                <th mat-header-cell *matHeaderCellDef></th>\n                <td mat-cell *matCellDef=\"let user\">\n                  <div class=\"user-avatar\">\n                    {{getInitials(user.firstName, user.lastName)}}\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- Name Column -->\n              <ng-container matColumnDef=\"name\">\n                <th mat-header-cell *matHeaderCellDef>Name</th>\n                <td mat-cell *matCellDef=\"let user\">\n                  <div class=\"user-info\">\n                    <strong>{{getFullName(user)}}</strong>\n                    <small>{{getUserName(user)}}</small>\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- Email Column -->\n              <ng-container matColumnDef=\"email\">\n                <th mat-header-cell *matHeaderCellDef>Email</th>\n                <td mat-cell *matCellDef=\"let user\">{{getUserEmail(user)}}</td>\n              </ng-container>\n\n              <!-- Phone Column -->\n              <ng-container matColumnDef=\"phone\">\n                <th mat-header-cell *matHeaderCellDef>Phone</th>\n                <td mat-cell *matCellDef=\"let user\">{{getUserPhone(user) || 'N/A'}}</td>\n              </ng-container>\n\n              <!-- Roles Column -->\n              <ng-container matColumnDef=\"roles\">\n                <th mat-header-cell *matHeaderCellDef>Roles</th>\n                <td mat-cell *matCellDef=\"let user\">\n                  <div class=\"roles-container\">\n                    <mat-chip *ngFor=\"let role of getUserRoles(user)\" [class]=\"getRoleClass(role)\">\n                      {{role}}\n                    </mat-chip>\n                  </div>\n                </td>\n              </ng-container>\n\n              <!-- Actions Column -->\n              <ng-container matColumnDef=\"actions\">\n                <th mat-header-cell *matHeaderCellDef>Actions</th>\n                <td mat-cell *matCellDef=\"let user\">\n                  <div class=\"action-buttons\">\n                    <button mat-icon-button (click)=\"editUser(user)\" matTooltip=\"Edit User\">\n                      <mat-icon>edit</mat-icon>\n                    </button>\n                    <button mat-icon-button (click)=\"manageRoles(user)\" matTooltip=\"Manage Roles\">\n                      <mat-icon>admin_panel_settings</mat-icon>\n                    </button>\n                    <button mat-icon-button color=\"warn\" (click)=\"deleteUser(user)\" matTooltip=\"Delete User\">\n                      <mat-icon>delete</mat-icon>\n                    </button>\n                  </div>\n                </td>\n              </ng-container>\n\n              <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n              <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" class=\"user-row\"></tr>\n            </table>\n\n            <!-- No Data Message -->\n            <div *ngIf=\"users.length === 0\" class=\"no-data\">\n              <mat-icon>people_outline</mat-icon>\n              <h3>No users found</h3>\n              <p>No users match your current search criteria.</p>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <mat-paginator\n            *ngIf=\"!loading && totalCount > 0\"\n            [length]=\"totalCount\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"[5, 10, 25, 50]\"\n            [pageIndex]=\"currentPage - 1\"\n            (page)=\"onPageChange($event)\"\n            showFirstLastButtons>\n          </mat-paginator>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .user-management-container {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .filters {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .filters mat-form-field {\n      min-width: 200px;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 2rem;\n    }\n\n    .table-container {\n      overflow-x: auto;\n    }\n\n    .users-table {\n      width: 100%;\n    }\n\n    .user-row:hover {\n      background-color: #f5f5f5;\n    }\n\n    .user-avatar {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #2196f3;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      font-size: 0.9rem;\n    }\n\n    .user-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .user-info strong {\n      font-size: 0.95rem;\n    }\n\n    .user-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .roles-container {\n      display: flex;\n      gap: 0.25rem;\n      flex-wrap: wrap;\n    }\n\n    .role-employee {\n      background-color: #e3f2fd;\n      color: #1976d2;\n    }\n\n    .role-manager {\n      background-color: #f3e5f5;\n      color: #7b1fa2;\n    }\n\n    .role-hr {\n      background-color: #e8f5e8;\n      color: #2e7d32;\n    }\n\n    .role-admin {\n      background-color: #fff3e0;\n      color: #ef6c00;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.25rem;\n    }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #ccc;\n    }\n\n    @media (max-width: 768px) {\n      .filters {\n        flex-direction: column;\n      }\n\n      .filters mat-form-field {\n        min-width: 100%;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class UserManagementComponent implements OnInit, OnDestroy {\n  private readonly destroy$ = new Subject<void>();\n  private readonly searchSubject = new Subject<string>();\n\n  users: UserDto[] = [];\n  displayedColumns: string[] = ['avatar', 'name', 'email', 'phone', 'roles', 'actions'];\n  loading = false;\n\n  // Pagination\n  totalCount = 0;\n  currentPage = 1;\n  pageSize = 10;\n\n  // Filters\n  searchTerm = '';\n  selectedRole = '';\n\n  constructor(\n    private readonly dialog: MatDialog,\n    private readonly snackBar: MatSnackBar,\n    private readonly userService: UserService\n  ) {\n    // Setup search debouncing\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      takeUntil(this.destroy$)\n    ).subscribe(() => {\n      this.currentPage = 1;\n      this.loadUsers();\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadUsers();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadUsers(): void {\n    this.loading = true;\n\n    // Build pagination parameters\n    const params: PaginationParams = {\n      pageNumber: this.currentPage,\n      pageSize: this.pageSize,\n      searchTerm: this.searchTerm || undefined,\n      sortBy: 'userName', // Default sort by username\n      sortDirection: 'asc'\n    };\n\n    // Load users from backend based on filter\n    if (this.selectedRole) {\n      // Load users by role (returns PaginatedResponse)\n      this.userService.getUsersByRole(this.selectedRole, params)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (response) => {\n            this.users = response.data;\n            this.totalCount = response.totalCount;\n            this.loading = false;\n          },\n          error: (error: any) => {\n            console.error('Error loading users by role:', error);\n            this.handleLoadError();\n          }\n        });\n    } else {\n      // Load all users (returns UserDto[])\n      this.userService.getUsers(params)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (users: UserDto[]) => {\n            this.users = users;\n            this.totalCount = users.length; // Note: This should ideally come from a paginated response\n            this.loading = false;\n          },\n          error: (error: any) => {\n            console.error('Error loading users:', error);\n            this.handleLoadError();\n          }\n        });\n    }\n  }\n\n  private handleLoadError(): void {\n    this.snackBar.open('Error loading users. Please try again.', 'Close', {\n      duration: 5000,\n      panelClass: ['error-snackbar']\n    });\n\n    // Fallback to mock data on error\n    this.users = this.getMockUsers();\n    this.totalCount = this.users.length;\n    this.loading = false;\n  }\n\n  getMockUsers(): UserDto[] {\n    return [\n      {\n        Id: '1',\n        UserName: 'john.doe',\n        Email: '<EMAIL>',\n        FirstName: 'John',\n        LastName: 'Doe',\n        PhoneNumber: '******-0123',\n        Roles: ['Employee']\n      },\n      {\n        Id: '2',\n        UserName: 'jane.smith',\n        Email: '<EMAIL>',\n        FirstName: 'Jane',\n        LastName: 'Smith',\n        PhoneNumber: '******-0124',\n        Roles: ['Manager']\n      },\n      {\n        Id: '3',\n        UserName: 'admin',\n        Email: '<EMAIL>',\n        FirstName: 'System',\n        LastName: 'Administrator',\n        PhoneNumber: '******-0100',\n        Roles: ['Admin']\n      }\n    ];\n  }\n\n  onSearchChange(): void {\n    this.searchSubject.next(this.searchTerm);\n  }\n\n  onFilterChange(): void {\n    this.currentPage = 1;\n    this.loadUsers();\n  }\n\n  onPageChange(event: PageEvent): void {\n    this.currentPage = event.pageIndex + 1;\n    this.pageSize = event.pageSize;\n    this.loadUsers();\n  }\n\n  getInitials(firstName?: string, lastName?: string): string {\n    const first = firstName?.charAt(0) || '';\n    const last = lastName?.charAt(0) || '';\n    return (first + last).toUpperCase() || '?';\n  }\n\n  getFullName(user: UserDto): string {\n    const firstName = user.FirstName || user.firstName || '';\n    const lastName = user.LastName || user.lastName || '';\n    const userName = user.UserName || user.userName || '';\n    return `${firstName} ${lastName}`.trim() || userName;\n  }\n\n  // Helper methods to handle both property naming conventions\n  getUserName(user: UserDto): string {\n    return user.UserName || user.userName || '';\n  }\n\n  getUserEmail(user: UserDto): string {\n    return user.Email || user.email || '';\n  }\n\n  getUserPhone(user: UserDto): string {\n    return user.PhoneNumber || user.phoneNumber || '';\n  }\n\n  getUserRoles(user: UserDto): string[] {\n    return user.Roles || user.roles || [];\n  }\n\n  getUserId(user: UserDto): string {\n    return user.Id || user.id || '';\n  }\n\n  getRoleClass(role: string): string {\n    return `role-${role.toLowerCase()}`;\n  }\n\n  openCreateUserDialog(): void {\n    this.snackBar.open('Create user dialog would open here', 'Close', { duration: 3000 });\n  }\n\n  editUser(user: UserDto): void {\n    this.snackBar.open(`Edit user: ${this.getUserName(user)}`, 'Close', { duration: 3000 });\n  }\n\n  manageRoles(user: UserDto): void {\n    this.snackBar.open(`Manage roles for: ${this.getUserName(user)}`, 'Close', { duration: 3000 });\n  }\n\n  deleteUser(user: UserDto): void {\n    const userName = this.getUserName(user);\n    const userId = this.getUserId(user);\n\n    // Show confirmation dialog\n    const confirmed = confirm(`Are you sure you want to delete user \"${userName}\"? This action cannot be undone.`);\n\n    if (confirmed) {\n      this.userService.deleteUser(userId)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: () => {\n            this.snackBar.open(`User \"${userName}\" deleted successfully`, 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            // Reload users to reflect the change\n            this.loadUsers();\n          },\n          error: (error: any) => {\n            console.error('Error deleting user:', error);\n            this.snackBar.open(`Error deleting user \"${userName}\". Please try again.`, 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkFU,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAOM,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA;AAEhC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM;;;;;AADJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,QAAA,WAAA,QAAA,QAAA,GAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA,EACX,GAAA,QAAA;AACb,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAC7B,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA,EAAQ,EAChC;;;;;AAFI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,CAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,CAAA;;;;;AAOX,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;;;;;AAC3C,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;;;;;AAAtB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,OAAA,CAAA;;;;;AAKpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;;;;;AAC3C,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA;;;;;AAA/B,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,OAAA,KAAA,KAAA;;;;;AAKpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;;;;;AAGvC,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAFkD,IAAA,qBAAA,OAAA,aAAA,OAAA,CAAA;AAChD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,GAAA;;;;;AAHN,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA;AAEhC,IAAA,qBAAA,GAAA,0DAAA,GAAA,GAAA,YAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;;;;;AAHuB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA,OAAA,CAAA;;;;;AAS/B,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,OAAA,EAAA,EACN,GAAA,UAAA,EAAA;AACF,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,OAAA,CAAc;IAAA,CAAA;AAC7C,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA,EAAW;AAE3B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAwB,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,OAAA,CAAiB;IAAA,CAAA;AAChD,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA,EAAW;AAE3C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAqC,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,OAAA,CAAgB;IAAA,CAAA;AAC5D,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA,EAAW,EACpB,EACL;;;;;AAIV,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgD,GAAA,UAAA;AACpC,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA;AAClB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,8CAAA;AAA4C,IAAA,uBAAA,EAAI;;;;;AAzEvD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,SAAA,EAAA;AAG1C,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,8CAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,8CAAA,GAAA,GAAA,MAAA,EAAA;;AAUxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA;;AAKxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA;;AAKxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA;;AAWxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,+CAAA,IAAA,GAAA,MAAA,EAAA;;AAgBxC,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA,EAAuD,IAAA,+CAAA,GAAA,GAAA,MAAA,EAAA;AAEzD,IAAA,uBAAA;AAGA,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA;;;;AA1EmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,OAAA,KAAA;AAgEK,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,gBAAA;AACa,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,gBAAA;AAI7B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,MAAA,WAAA,CAAA;;;;;;AAQR,IAAA,yBAAA,GAAA,iBAAA,EAAA;AAME,IAAA,qBAAA,QAAA,SAAA,gFAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAQ,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA;AAE9B,IAAA,uBAAA;;;;AANE,IAAA,qBAAA,UAAA,OAAA,UAAA,EAAqB,YAAA,OAAA,QAAA,EACA,mBAAA,0BAAA,GAAA,GAAA,CAAA,EACc,aAAA,OAAA,cAAA,CAAA;;;AAiJzC,IAAO,0BAAP,MAAO,yBAAuB;EAkBf;EACA;EACA;EAnBF,WAAW,IAAI,QAAO;EACtB,gBAAgB,IAAI,QAAO;EAE5C,QAAmB,CAAA;EACnB,mBAA6B,CAAC,UAAU,QAAQ,SAAS,SAAS,SAAS,SAAS;EACpF,UAAU;;EAGV,aAAa;EACb,cAAc;EACd,WAAW;;EAGX,aAAa;EACb,eAAe;EAEf,YACmB,QACA,UACA,aAAwB;AAFxB,SAAA,SAAA;AACA,SAAA,WAAA;AACA,SAAA,cAAA;AAGjB,SAAK,cAAc,KACjB,aAAa,GAAG,GAChB,qBAAoB,GACpB,UAAU,KAAK,QAAQ,CAAC,EACxB,UAAU,MAAK;AACf,WAAK,cAAc;AACnB,WAAK,UAAS;IAChB,CAAC;EACH;EAEA,WAAQ;AACN,SAAK,UAAS;EAChB;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEA,YAAS;AACP,SAAK,UAAU;AAGf,UAAM,SAA2B;MAC/B,YAAY,KAAK;MACjB,UAAU,KAAK;MACf,YAAY,KAAK,cAAc;MAC/B,QAAQ;;MACR,eAAe;;AAIjB,QAAI,KAAK,cAAc;AAErB,WAAK,YAAY,eAAe,KAAK,cAAc,MAAM,EACtD,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU;QACT,MAAM,CAAC,aAAY;AACjB,eAAK,QAAQ,SAAS;AACtB,eAAK,aAAa,SAAS;AAC3B,eAAK,UAAU;QACjB;QACA,OAAO,CAAC,UAAc;AACpB,kBAAQ,MAAM,gCAAgC,KAAK;AACnD,eAAK,gBAAe;QACtB;OACD;IACL,OAAO;AAEL,WAAK,YAAY,SAAS,MAAM,EAC7B,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU;QACT,MAAM,CAAC,UAAoB;AACzB,eAAK,QAAQ;AACb,eAAK,aAAa,MAAM;AACxB,eAAK,UAAU;QACjB;QACA,OAAO,CAAC,UAAc;AACpB,kBAAQ,MAAM,wBAAwB,KAAK;AAC3C,eAAK,gBAAe;QACtB;OACD;IACL;EACF;EAEQ,kBAAe;AACrB,SAAK,SAAS,KAAK,0CAA0C,SAAS;MACpE,UAAU;MACV,YAAY,CAAC,gBAAgB;KAC9B;AAGD,SAAK,QAAQ,KAAK,aAAY;AAC9B,SAAK,aAAa,KAAK,MAAM;AAC7B,SAAK,UAAU;EACjB;EAEA,eAAY;AACV,WAAO;MACL;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,OAAO,CAAC,UAAU;;MAEpB;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,OAAO,CAAC,SAAS;;MAEnB;QACE,IAAI;QACJ,UAAU;QACV,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,OAAO,CAAC,OAAO;;;EAGrB;EAEA,iBAAc;AACZ,SAAK,cAAc,KAAK,KAAK,UAAU;EACzC;EAEA,iBAAc;AACZ,SAAK,cAAc;AACnB,SAAK,UAAS;EAChB;EAEA,aAAa,OAAgB;AAC3B,SAAK,cAAc,MAAM,YAAY;AACrC,SAAK,WAAW,MAAM;AACtB,SAAK,UAAS;EAChB;EAEA,YAAY,WAAoB,UAAiB;AAC/C,UAAM,QAAQ,WAAW,OAAO,CAAC,KAAK;AACtC,UAAM,OAAO,UAAU,OAAO,CAAC,KAAK;AACpC,YAAQ,QAAQ,MAAM,YAAW,KAAM;EACzC;EAEA,YAAY,MAAa;AACvB,UAAM,YAAY,KAAK,aAAa,KAAK,aAAa;AACtD,UAAM,WAAW,KAAK,YAAY,KAAK,YAAY;AACnD,UAAM,WAAW,KAAK,YAAY,KAAK,YAAY;AACnD,WAAO,GAAG,SAAS,IAAI,QAAQ,GAAG,KAAI,KAAM;EAC9C;;EAGA,YAAY,MAAa;AACvB,WAAO,KAAK,YAAY,KAAK,YAAY;EAC3C;EAEA,aAAa,MAAa;AACxB,WAAO,KAAK,SAAS,KAAK,SAAS;EACrC;EAEA,aAAa,MAAa;AACxB,WAAO,KAAK,eAAe,KAAK,eAAe;EACjD;EAEA,aAAa,MAAa;AACxB,WAAO,KAAK,SAAS,KAAK,SAAS,CAAA;EACrC;EAEA,UAAU,MAAa;AACrB,WAAO,KAAK,MAAM,KAAK,MAAM;EAC/B;EAEA,aAAa,MAAY;AACvB,WAAO,QAAQ,KAAK,YAAW,CAAE;EACnC;EAEA,uBAAoB;AAClB,SAAK,SAAS,KAAK,sCAAsC,SAAS,EAAE,UAAU,IAAI,CAAE;EACtF;EAEA,SAAS,MAAa;AACpB,SAAK,SAAS,KAAK,cAAc,KAAK,YAAY,IAAI,CAAC,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;EACxF;EAEA,YAAY,MAAa;AACvB,SAAK,SAAS,KAAK,qBAAqB,KAAK,YAAY,IAAI,CAAC,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;EAC/F;EAEA,WAAW,MAAa;AACtB,UAAM,WAAW,KAAK,YAAY,IAAI;AACtC,UAAM,SAAS,KAAK,UAAU,IAAI;AAGlC,UAAM,YAAY,QAAQ,yCAAyC,QAAQ,kCAAkC;AAE7G,QAAI,WAAW;AACb,WAAK,YAAY,WAAW,MAAM,EAC/B,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU;QACT,MAAM,MAAK;AACT,eAAK,SAAS,KAAK,SAAS,QAAQ,0BAA0B,SAAS;YACrE,UAAU;YACV,YAAY,CAAC,kBAAkB;WAChC;AAED,eAAK,UAAS;QAChB;QACA,OAAO,CAAC,UAAc;AACpB,kBAAQ,MAAM,wBAAwB,KAAK;AAC3C,eAAK,SAAS,KAAK,wBAAwB,QAAQ,wBAAwB,SAAS;YAClF,UAAU;YACV,YAAY,CAAC,gBAAgB;WAC9B;QACH;OACD;IACL;EACF;;qCAjOW,0BAAuB,4BAAA,SAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,2BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,eAAA,mBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,aAAA,EAAA,GAAA,CAAA,GAAA,iBAAA,mBAAA,SAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,wBAAA,IAAA,GAAA,UAAA,YAAA,mBAAA,aAAA,QAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,aAAA,IAAA,GAAA,eAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,QAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,SAAA,YAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,IAAA,cAAA,aAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,cAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,QAAA,cAAA,eAAA,GAAA,OAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,IAAA,GAAA,UAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,wBAAA,IAAA,GAAA,QAAA,UAAA,YAAA,mBAAA,WAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA7QhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuC,GAAA,UAAA,EAC3B,GAAA,iBAAA,EACS,GAAA,gBAAA,EACC,GAAA,UAAA;AACJ,MAAA,iBAAA,GAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,iBAAA,GAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACgB,MAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,eAAS,IAAA,qBAAA;MAAsB,CAAA;AACvE,MAAA,yBAAA,GAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACpB,MAAA,iBAAA,IAAA,YAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,yBAAA,IAAA,kBAAA,EAAkB,IAAA,OAAA,CAAA,EAEK,IAAA,kBAAA,CAAA,EACkB,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AACjB,MAAA,yBAAA,IAAA,SAAA,CAAA;AAAgB,MAAA,2BAAA,iBAAA,SAAA,iEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,eAAA;MAAA,CAAA;AAAyB,MAAA,qBAAA,iBAAA,SAAA,mEAAA;AAAA,eAAiB,IAAA,eAAA;MAAgB,CAAA;AAA1E,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,YAAA,CAAA;AAAoB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA,EAAW;AAGvC,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACf,MAAA,yBAAA,IAAA,cAAA,CAAA;AAAY,MAAA,2BAAA,iBAAA,SAAA,sEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,cAAA,MAAA,MAAA,IAAA,eAAA;AAAA,eAAA;MAAA,CAAA;AAA2B,MAAA,qBAAA,mBAAA,SAAA,0EAAA;AAAA,eAAmB,IAAA,eAAA;MAAgB,CAAA;AACxE,MAAA,yBAAA,IAAA,cAAA,CAAA;AAAqB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,cAAA,CAAA;AAA6B,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,cAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,IAAA;AAAE,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,cAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAa,EACjC,EACE;AAInB,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA+C,IAAA,yCAAA,IAAA,GAAA,OAAA,EAAA,EAKD,IAAA,mDAAA,GAAA,GAAA,iBAAA,EAAA;AAuFhD,MAAA,uBAAA,EAAmB,EACV;;;AA9Ga,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,UAAA;AAMJ,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,YAAA;AAWV,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;AA+EH,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,aAAA,CAAA;;;IA5IT;IAAY;IAAA;IACZ;IACA;IAAW;IAAA;IAAA;IACX;IACA;IAAc;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACd;IAAe;IAAA;IACf;IAAa;IACb;IAAa;IAAA;IAAA;IAAA;IACb;IAAc;IACd;IAAkB;IAClB;IAAkB;IAAA;IAAA;IAClB;IAAc;IACd;IAAe;IAAA;IACf;IAAwB;IACxB;IACA;IACA;IAAgB;EAAA,GAAA,QAAA,CAAA,u6EAAA,EAAA,CAAA;;;sEAgRP,yBAAuB,CAAA;UApSnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqIT,QAAA,CAAA,wlEAAA,EAAA,CAAA;;;;6EAyIU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}