import {
  <PERSON><PERSON><PERSON><PERSON>oggle,
  MatSlideToggleModule
} from "./chunk-IPVZCAF6.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-43VBYOSE.js";
import {
  Mat<PERSON>ivider,
  MatDividerModule
} from "./chunk-7XGPZDLL.js";
import "./chunk-XSFMJNUM.js";
import "./chunk-TCMHSQZD.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-DKEYCCVZ.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-3EIBYAGU.js";
import "./chunk-W6VU2H2S.js";
import {
  MatForm<PERSON>ield,
  MatLabel
} from "./chunk-R3ISBMK2.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  MaxValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NumberV<PERSON>ueAccessor,
  ReactiveFormsModule,
  Valida<PERSON>,
  ɵNgNoValidate
} from "./chunk-D2SQUTFC.js";
import {
  MatTab,
  MatTabGroup,
  MatTabsModule
} from "./chunk-FOR5HVNB.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  CommonModule,
  DatePipe
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/features/admin/components/system-settings/system-settings.component.ts
var SystemSettingsComponent = class _SystemSettingsComponent {
  fb;
  snackBar;
  generalForm;
  notificationForm;
  lastBackupDate = /* @__PURE__ */ new Date();
  lastBackupSize = "2.3 GB";
  constructor(fb, snackBar) {
    this.fb = fb;
    this.snackBar = snackBar;
    this.initForms();
  }
  ngOnInit() {
    this.loadSettings();
  }
  initForms() {
    this.generalForm = this.fb.group({
      applicationName: ["BPM Light", Validators.required],
      companyName: ["Your Company", Validators.required],
      defaultLanguage: ["en"],
      timezone: ["UTC"],
      maintenanceMode: [false],
      sessionTimeout: [60, [Validators.min(5), Validators.max(480)]],
      passwordMinLength: [8, [Validators.min(6), Validators.max(20)]],
      requirePasswordComplexity: [true],
      enableTwoFactor: [false]
    });
    this.notificationForm = this.fb.group({
      smtpServer: ["smtp.company.com"],
      smtpPort: [587],
      fromEmail: ["<EMAIL>", Validators.email],
      enableEmailNotifications: [true],
      notifyOnRequestSubmission: [true],
      notifyOnRequestApproval: [true],
      notifyOnOverdueRequests: [true]
    });
  }
  loadSettings() {
    console.log("Loading system settings...");
  }
  saveGeneralSettings() {
    if (this.generalForm.valid) {
      this.snackBar.open("General settings saved successfully", "Close", { duration: 3e3 });
    }
  }
  saveNotificationSettings() {
    if (this.notificationForm.valid) {
      this.snackBar.open("Notification settings saved successfully", "Close", { duration: 3e3 });
    }
  }
  createBackup() {
    this.snackBar.open("Creating backup...", "Close", { duration: 3e3 });
  }
  downloadBackup() {
    this.snackBar.open("Downloading backup...", "Close", { duration: 3e3 });
  }
  clearCache() {
    this.snackBar.open("Cache cleared successfully", "Close", { duration: 3e3 });
  }
  optimizeDatabase() {
    this.snackBar.open("Database optimization started", "Close", { duration: 3e3 });
  }
  restartSystem() {
    this.snackBar.open("System restart initiated", "Close", { duration: 3e3 });
  }
  static \u0275fac = function SystemSettingsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SystemSettingsComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SystemSettingsComponent, selectors: [["app-system-settings"]], decls: 176, vars: 7, consts: [[1, "system-settings-container"], ["label", "General"], [1, "tab-content"], [3, "ngSubmit", "formGroup"], [1, "settings-section"], [1, "form-row"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "applicationName"], ["matInput", "", "formControlName", "companyName"], ["appearance", "outline"], ["formControlName", "defaultLanguage"], ["value", "en"], ["value", "fr"], ["value", "es"], ["formControlName", "timezone"], ["value", "UTC"], ["value", "America/New_York"], ["value", "America/Los_Angeles"], ["value", "Europe/London"], [1, "toggle-section"], ["formControlName", "maintenanceMode"], [1, "toggle-description"], ["matInput", "", "type", "number", "formControlName", "sessionTimeout", "min", "5", "max", "480"], ["matInput", "", "type", "number", "formControlName", "passwordMinLength", "min", "6", "max", "20"], ["formControlName", "requirePasswordComplexity"], ["formControlName", "enableTwoFactor"], [1, "actions"], ["type", "submit", "mat-raised-button", "", "color", "primary"], ["label", "Notifications"], ["matInput", "", "formControlName", "smtpServer"], ["matInput", "", "type", "number", "formControlName", "smtpPort"], ["matInput", "", "type", "email", "formControlName", "fromEmail"], ["formControlName", "enableEmailNotifications"], ["formControlName", "notifyOnRequestSubmission"], ["formControlName", "notifyOnRequestApproval"], ["formControlName", "notifyOnOverdueRequests"], ["label", "Backup"], [1, "backup-info"], [1, "backup-status", "success"], [1, "backup-actions"], ["mat-raised-button", "", "color", "primary", 3, "click"], ["mat-button", "", 3, "click"], [1, "maintenance-actions"], ["mat-raised-button", "", 3, "click"], ["mat-raised-button", "", "color", "warn", 3, "click"]], template: function SystemSettingsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "settings");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " System Settings ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "mat-card-content")(8, "mat-tab-group")(9, "mat-tab", 1)(10, "div", 2)(11, "form", 3);
      \u0275\u0275listener("ngSubmit", function SystemSettingsComponent_Template_form_ngSubmit_11_listener() {
        return ctx.saveGeneralSettings();
      });
      \u0275\u0275elementStart(12, "div", 4)(13, "h3");
      \u0275\u0275text(14, "Application Settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "div", 5)(16, "mat-form-field", 6)(17, "mat-label");
      \u0275\u0275text(18, "Application Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(19, "input", 7);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "div", 5)(21, "mat-form-field", 6)(22, "mat-label");
      \u0275\u0275text(23, "Company Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(24, "input", 8);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(25, "div", 5)(26, "mat-form-field", 9)(27, "mat-label");
      \u0275\u0275text(28, "Default Language");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "mat-select", 10)(30, "mat-option", 11);
      \u0275\u0275text(31, "English");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "mat-option", 12);
      \u0275\u0275text(33, "French");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "mat-option", 13);
      \u0275\u0275text(35, "Spanish");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(36, "mat-form-field", 9)(37, "mat-label");
      \u0275\u0275text(38, "Timezone");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "mat-select", 14)(40, "mat-option", 15);
      \u0275\u0275text(41, "UTC");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "mat-option", 16);
      \u0275\u0275text(43, "Eastern Time");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "mat-option", 17);
      \u0275\u0275text(45, "Pacific Time");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "mat-option", 18);
      \u0275\u0275text(47, "London");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(48, "div", 19)(49, "mat-slide-toggle", 20);
      \u0275\u0275text(50, " Maintenance Mode ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "p", 21);
      \u0275\u0275text(52, "Enable maintenance mode to prevent user access during updates");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(53, "mat-divider");
      \u0275\u0275elementStart(54, "div", 4)(55, "h3");
      \u0275\u0275text(56, "Security Settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(57, "div", 5)(58, "mat-form-field", 9)(59, "mat-label");
      \u0275\u0275text(60, "Session Timeout (minutes)");
      \u0275\u0275elementEnd();
      \u0275\u0275element(61, "input", 22);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(62, "mat-form-field", 9)(63, "mat-label");
      \u0275\u0275text(64, "Password Min Length");
      \u0275\u0275elementEnd();
      \u0275\u0275element(65, "input", 23);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(66, "div", 19)(67, "mat-slide-toggle", 24);
      \u0275\u0275text(68, " Require Password Complexity ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "p", 21);
      \u0275\u0275text(70, "Require uppercase, lowercase, numbers, and special characters");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(71, "div", 19)(72, "mat-slide-toggle", 25);
      \u0275\u0275text(73, " Enable Two-Factor Authentication ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "p", 21);
      \u0275\u0275text(75, "Require 2FA for all admin users");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(76, "div", 26)(77, "button", 27)(78, "mat-icon");
      \u0275\u0275text(79, "save");
      \u0275\u0275elementEnd();
      \u0275\u0275text(80, " Save General Settings ");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(81, "mat-tab", 28)(82, "div", 2)(83, "form", 3);
      \u0275\u0275listener("ngSubmit", function SystemSettingsComponent_Template_form_ngSubmit_83_listener() {
        return ctx.saveNotificationSettings();
      });
      \u0275\u0275elementStart(84, "div", 4)(85, "h3");
      \u0275\u0275text(86, "Email Notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(87, "div", 5)(88, "mat-form-field", 6)(89, "mat-label");
      \u0275\u0275text(90, "SMTP Server");
      \u0275\u0275elementEnd();
      \u0275\u0275element(91, "input", 29);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(92, "div", 5)(93, "mat-form-field", 9)(94, "mat-label");
      \u0275\u0275text(95, "SMTP Port");
      \u0275\u0275elementEnd();
      \u0275\u0275element(96, "input", 30);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(97, "mat-form-field", 9)(98, "mat-label");
      \u0275\u0275text(99, "From Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(100, "input", 31);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(101, "div", 19)(102, "mat-slide-toggle", 32);
      \u0275\u0275text(103, " Enable Email Notifications ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(104, "p", 21);
      \u0275\u0275text(105, "Send email notifications for workflow events");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(106, "mat-divider");
      \u0275\u0275elementStart(107, "div", 4)(108, "h3");
      \u0275\u0275text(109, "System Notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(110, "div", 19)(111, "mat-slide-toggle", 33);
      \u0275\u0275text(112, " Request Submission ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(113, "p", 21);
      \u0275\u0275text(114, "Notify managers when new requests are submitted");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(115, "div", 19)(116, "mat-slide-toggle", 34);
      \u0275\u0275text(117, " Request Approval ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(118, "p", 21);
      \u0275\u0275text(119, "Notify requesters when their requests are approved/rejected");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(120, "div", 19)(121, "mat-slide-toggle", 35);
      \u0275\u0275text(122, " Overdue Requests ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(123, "p", 21);
      \u0275\u0275text(124, "Send reminders for overdue approval requests");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(125, "div", 26)(126, "button", 27)(127, "mat-icon");
      \u0275\u0275text(128, "save");
      \u0275\u0275elementEnd();
      \u0275\u0275text(129, " Save Notification Settings ");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(130, "mat-tab", 36)(131, "div", 2)(132, "div", 4)(133, "h3");
      \u0275\u0275text(134, "Database Backup");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(135, "div", 37)(136, "p")(137, "strong");
      \u0275\u0275text(138, "Last Backup:");
      \u0275\u0275elementEnd();
      \u0275\u0275text(139);
      \u0275\u0275pipe(140, "date");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(141, "p")(142, "strong");
      \u0275\u0275text(143, "Backup Size:");
      \u0275\u0275elementEnd();
      \u0275\u0275text(144);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(145, "p")(146, "strong");
      \u0275\u0275text(147, "Status:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(148, "span", 38);
      \u0275\u0275text(149, "Healthy");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(150, "div", 39)(151, "button", 40);
      \u0275\u0275listener("click", function SystemSettingsComponent_Template_button_click_151_listener() {
        return ctx.createBackup();
      });
      \u0275\u0275elementStart(152, "mat-icon");
      \u0275\u0275text(153, "backup");
      \u0275\u0275elementEnd();
      \u0275\u0275text(154, " Create Backup Now ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(155, "button", 41);
      \u0275\u0275listener("click", function SystemSettingsComponent_Template_button_click_155_listener() {
        return ctx.downloadBackup();
      });
      \u0275\u0275elementStart(156, "mat-icon");
      \u0275\u0275text(157, "download");
      \u0275\u0275elementEnd();
      \u0275\u0275text(158, " Download Latest Backup ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(159, "mat-divider");
      \u0275\u0275elementStart(160, "div", 4)(161, "h3");
      \u0275\u0275text(162, "System Maintenance");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(163, "div", 42)(164, "button", 43);
      \u0275\u0275listener("click", function SystemSettingsComponent_Template_button_click_164_listener() {
        return ctx.clearCache();
      });
      \u0275\u0275elementStart(165, "mat-icon");
      \u0275\u0275text(166, "clear_all");
      \u0275\u0275elementEnd();
      \u0275\u0275text(167, " Clear Cache ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(168, "button", 43);
      \u0275\u0275listener("click", function SystemSettingsComponent_Template_button_click_168_listener() {
        return ctx.optimizeDatabase();
      });
      \u0275\u0275elementStart(169, "mat-icon");
      \u0275\u0275text(170, "tune");
      \u0275\u0275elementEnd();
      \u0275\u0275text(171, " Optimize Database ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(172, "button", 44);
      \u0275\u0275listener("click", function SystemSettingsComponent_Template_button_click_172_listener() {
        return ctx.restartSystem();
      });
      \u0275\u0275elementStart(173, "mat-icon");
      \u0275\u0275text(174, "restart_alt");
      \u0275\u0275elementEnd();
      \u0275\u0275text(175, " Restart System ");
      \u0275\u0275elementEnd()()()()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(11);
      \u0275\u0275property("formGroup", ctx.generalForm);
      \u0275\u0275advance(72);
      \u0275\u0275property("formGroup", ctx.notificationForm);
      \u0275\u0275advance(56);
      \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(140, 4, ctx.lastBackupDate, "full"), "");
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate1(" ", ctx.lastBackupSize, "");
    }
  }, dependencies: [
    CommonModule,
    DatePipe,
    FormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NumberValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    MinValidator,
    MaxValidator,
    ReactiveFormsModule,
    FormGroupDirective,
    FormControlName,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatInputModule,
    MatInput,
    MatSelectModule,
    MatSelect,
    MatOption,
    MatSlideToggleModule,
    MatSlideToggle,
    MatDividerModule,
    MatDivider,
    MatSnackBarModule,
    MatTabsModule,
    MatTab,
    MatTabGroup
  ], styles: ["\n\n.system-settings-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.tab-content[_ngcontent-%COMP%] {\n  padding: 1rem 0;\n}\n.settings-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.settings-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n  color: #333;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.toggle-section[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.toggle-description[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n  font-size: 0.9rem;\n}\n.actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.backup-info[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n}\n.backup-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0;\n}\n.backup-status[_ngcontent-%COMP%] {\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: bold;\n}\n.backup-status.success[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.backup-actions[_ngcontent-%COMP%], \n.maintenance-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .form-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .backup-actions[_ngcontent-%COMP%], \n   .maintenance-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=system-settings.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SystemSettingsComponent, [{
    type: Component,
    args: [{ selector: "app-system-settings", standalone: true, imports: [
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatSlideToggleModule,
      MatDividerModule,
      MatSnackBarModule,
      MatTabsModule
    ], template: `
    <div class="system-settings-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>settings</mat-icon>
            System Settings
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <mat-tab-group>
            <!-- General Settings -->
            <mat-tab label="General">
              <div class="tab-content">
                <form [formGroup]="generalForm" (ngSubmit)="saveGeneralSettings()">
                  <div class="settings-section">
                    <h3>Application Settings</h3>
                    
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Application Name</mat-label>
                        <input matInput formControlName="applicationName">
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Company Name</mat-label>
                        <input matInput formControlName="companyName">
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Default Language</mat-label>
                        <mat-select formControlName="defaultLanguage">
                          <mat-option value="en">English</mat-option>
                          <mat-option value="fr">French</mat-option>
                          <mat-option value="es">Spanish</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Timezone</mat-label>
                        <mat-select formControlName="timezone">
                          <mat-option value="UTC">UTC</mat-option>
                          <mat-option value="America/New_York">Eastern Time</mat-option>
                          <mat-option value="America/Los_Angeles">Pacific Time</mat-option>
                          <mat-option value="Europe/London">London</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="maintenanceMode">
                        Maintenance Mode
                      </mat-slide-toggle>
                      <p class="toggle-description">Enable maintenance mode to prevent user access during updates</p>
                    </div>
                  </div>

                  <mat-divider></mat-divider>

                  <div class="settings-section">
                    <h3>Security Settings</h3>
                    
                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>Session Timeout (minutes)</mat-label>
                        <input matInput type="number" formControlName="sessionTimeout" min="5" max="480">
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Password Min Length</mat-label>
                        <input matInput type="number" formControlName="passwordMinLength" min="6" max="20">
                      </mat-form-field>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="requirePasswordComplexity">
                        Require Password Complexity
                      </mat-slide-toggle>
                      <p class="toggle-description">Require uppercase, lowercase, numbers, and special characters</p>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="enableTwoFactor">
                        Enable Two-Factor Authentication
                      </mat-slide-toggle>
                      <p class="toggle-description">Require 2FA for all admin users</p>
                    </div>
                  </div>

                  <div class="actions">
                    <button type="submit" mat-raised-button color="primary">
                      <mat-icon>save</mat-icon>
                      Save General Settings
                    </button>
                  </div>
                </form>
              </div>
            </mat-tab>

            <!-- Notification Settings -->
            <mat-tab label="Notifications">
              <div class="tab-content">
                <form [formGroup]="notificationForm" (ngSubmit)="saveNotificationSettings()">
                  <div class="settings-section">
                    <h3>Email Notifications</h3>
                    
                    <div class="form-row">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>SMTP Server</mat-label>
                        <input matInput formControlName="smtpServer">
                      </mat-form-field>
                    </div>

                    <div class="form-row">
                      <mat-form-field appearance="outline">
                        <mat-label>SMTP Port</mat-label>
                        <input matInput type="number" formControlName="smtpPort">
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>From Email</mat-label>
                        <input matInput type="email" formControlName="fromEmail">
                      </mat-form-field>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="enableEmailNotifications">
                        Enable Email Notifications
                      </mat-slide-toggle>
                      <p class="toggle-description">Send email notifications for workflow events</p>
                    </div>
                  </div>

                  <mat-divider></mat-divider>

                  <div class="settings-section">
                    <h3>System Notifications</h3>
                    
                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="notifyOnRequestSubmission">
                        Request Submission
                      </mat-slide-toggle>
                      <p class="toggle-description">Notify managers when new requests are submitted</p>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="notifyOnRequestApproval">
                        Request Approval
                      </mat-slide-toggle>
                      <p class="toggle-description">Notify requesters when their requests are approved/rejected</p>
                    </div>

                    <div class="toggle-section">
                      <mat-slide-toggle formControlName="notifyOnOverdueRequests">
                        Overdue Requests
                      </mat-slide-toggle>
                      <p class="toggle-description">Send reminders for overdue approval requests</p>
                    </div>
                  </div>

                  <div class="actions">
                    <button type="submit" mat-raised-button color="primary">
                      <mat-icon>save</mat-icon>
                      Save Notification Settings
                    </button>
                  </div>
                </form>
              </div>
            </mat-tab>

            <!-- Backup & Maintenance -->
            <mat-tab label="Backup">
              <div class="tab-content">
                <div class="settings-section">
                  <h3>Database Backup</h3>
                  
                  <div class="backup-info">
                    <p><strong>Last Backup:</strong> {{lastBackupDate | date:'full'}}</p>
                    <p><strong>Backup Size:</strong> {{lastBackupSize}}</p>
                    <p><strong>Status:</strong> <span class="backup-status success">Healthy</span></p>
                  </div>

                  <div class="backup-actions">
                    <button mat-raised-button color="primary" (click)="createBackup()">
                      <mat-icon>backup</mat-icon>
                      Create Backup Now
                    </button>
                    <button mat-button (click)="downloadBackup()">
                      <mat-icon>download</mat-icon>
                      Download Latest Backup
                    </button>
                  </div>
                </div>

                <mat-divider></mat-divider>

                <div class="settings-section">
                  <h3>System Maintenance</h3>
                  
                  <div class="maintenance-actions">
                    <button mat-raised-button (click)="clearCache()">
                      <mat-icon>clear_all</mat-icon>
                      Clear Cache
                    </button>
                    <button mat-raised-button (click)="optimizeDatabase()">
                      <mat-icon>tune</mat-icon>
                      Optimize Database
                    </button>
                    <button mat-raised-button color="warn" (click)="restartSystem()">
                      <mat-icon>restart_alt</mat-icon>
                      Restart System
                    </button>
                  </div>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;0c5798941cfe550eb4b1f9b3f71dad525c27d597308c8015059120b036391e44;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/admin/components/system-settings/system-settings.component.ts */\n.system-settings-container {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.tab-content {\n  padding: 1rem 0;\n}\n.settings-section {\n  margin-bottom: 2rem;\n}\n.settings-section h3 {\n  margin-bottom: 1rem;\n  color: #333;\n}\n.form-row {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.full-width {\n  width: 100%;\n}\n.toggle-section {\n  margin-bottom: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n}\n.toggle-description {\n  margin: 0.5rem 0 0 0;\n  color: #666;\n  font-size: 0.9rem;\n}\n.actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.backup-info {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n}\n.backup-info p {\n  margin: 0.5rem 0;\n}\n.backup-status {\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: bold;\n}\n.backup-status.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n.backup-actions,\n.maintenance-actions {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n@media (max-width: 768px) {\n  .form-row {\n    flex-direction: column;\n  }\n  .backup-actions,\n  .maintenance-actions {\n    flex-direction: column;\n  }\n}\n/*# sourceMappingURL=system-settings.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SystemSettingsComponent, { className: "SystemSettingsComponent", filePath: "src/app/features/admin/components/system-settings/system-settings.component.ts", lineNumber: 356 });
})();
export {
  SystemSettingsComponent
};
//# sourceMappingURL=chunk-5TCCNVFM.js.map
