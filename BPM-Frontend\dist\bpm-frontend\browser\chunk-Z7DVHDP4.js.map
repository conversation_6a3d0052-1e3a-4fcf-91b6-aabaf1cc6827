{"version": 3, "sources": ["src/app/features/admin/admin-routing.module.ts", "src/app/features/admin/admin.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { RoleGuard } from '../../core/guards';\n\nconst routes: Routes = [\n  {\n    path: '',\n    canActivate: [RoleGuard],\n    data: { roles: ['Admin'] },\n    loadComponent: () => import('./components/admin-layout/admin-layout.component').then(c => c.AdminLayoutComponent),\n    children: [\n      {\n        path: '',\n        redirectTo: 'users',\n        pathMatch: 'full'\n      },\n      {\n        path: 'users',\n        loadComponent: () => import('./components/user-management/user-management.component').then(c => c.UserManagementComponent)\n      },\n      {\n        path: 'workflow-designer',\n        loadComponent: () => import('./components/workflow-designer/workflow-designer.component').then(c => c.WorkflowDesignerComponent)\n      },\n      {\n        path: 'roles',\n        loadComponent: () => import('./components/role-management/role-management.component').then(c => c.RoleManagementComponent)\n      },\n      {\n        path: 'settings',\n        loadComponent: () => import('./components/system-settings/system-settings.component').then(c => c.SystemSettingsComponent)\n      },\n      {\n        path: 'reports',\n        loadComponent: () => import('./components/admin-reports/admin-reports.component').then(c => c.AdminReportsComponent)\n      },\n      {\n        path: '**',\n        loadComponent: () => import('./components/admin-not-found/admin-not-found.component').then(c => c.AdminNotFoundComponent)\n      }\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class AdminRoutingModule { }\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { AdminRoutingModule } from './admin-routing.module';\n\n@NgModule({\n  declarations: [],\n  imports: [\n    CommonModule,\n    RouterModule,\n    AdminRoutingModule\n  ]\n})\nexport class AdminModule { }\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA,IAAM,SAAiB;EACrB;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC;IACxB,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,oBAAoB;IAChH,UAAU;MACR;QACE,MAAM;QACN,YAAY;QACZ,WAAW;;MAEb;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwD,EAAE,KAAK,OAAK,EAAE,uBAAuB;;MAE3H;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAA4D,EAAE,KAAK,OAAK,EAAE,yBAAyB;;MAEjI;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwD,EAAE,KAAK,OAAK,EAAE,uBAAuB;;MAE3H;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwD,EAAE,KAAK,OAAK,EAAE,uBAAuB;;MAE3H;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAoD,EAAE,KAAK,OAAK,EAAE,qBAAqB;;MAErH;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwD,EAAE,KAAK,OAAK,EAAE,sBAAsB;;;;;AAU1H,IAAO,qBAAP,MAAO,oBAAkB;;qCAAlB,qBAAkB;EAAA;wEAAlB,oBAAkB,CAAA;4EAHnB,aAAa,SAAS,MAAM,GAC5B,YAAY,EAAA,CAAA;;;sEAEX,oBAAkB,CAAA;UAJ9B;WAAS;MACR,SAAS,CAAC,aAAa,SAAS,MAAM,CAAC;MACvC,SAAS,CAAC,YAAY;KACvB;;;;;ACjCK,IAAO,cAAP,MAAO,aAAW;;qCAAX,cAAW;EAAA;wEAAX,aAAW,CAAA;;IALpB;IACA;IACA;EAAkB,EAAA,CAAA;;;sEAGT,aAAW,CAAA;UARvB;WAAS;MACR,cAAc,CAAA;MACd,SAAS;QACP;QACA;QACA;;KAEH;;;", "names": []}