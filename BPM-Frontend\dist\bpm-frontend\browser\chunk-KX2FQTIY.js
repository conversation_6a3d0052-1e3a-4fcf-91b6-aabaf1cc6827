import {
  RouterLink,
  RouterLinkActive,
  RouterModule,
  RouterOutlet
} from "./chunk-ESNMJU6B.js";
import "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-JTRMQXMJ.js";

// src/app/features/admin/components/admin-layout/admin-layout.component.ts
var AdminLayoutComponent = class _AdminLayoutComponent {
  static \u0275fac = function AdminLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminLayoutComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminLayoutComponent, selectors: [["app-admin-layout"]], decls: 12, vars: 0, consts: [[1, "admin-layout"], [1, "sidebar"], ["routerLink", "users", "routerLinkActive", "active"], ["routerLink", "workflow-designer", "routerLinkActive", "active"], [1, "content"]], template: function AdminLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "aside", 1)(2, "nav")(3, "ul")(4, "li")(5, "a", 2);
      \u0275\u0275text(6, "User Management");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "li")(8, "a", 3);
      \u0275\u0275text(9, "Workflow Designer");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(10, "main", 4);
      \u0275\u0275element(11, "router-outlet");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [RouterModule, RouterOutlet, RouterLink, RouterLinkActive], styles: ["\n\n.admin-layout[_ngcontent-%COMP%] {\n  display: flex;\n  height: 100vh;\n}\n.admin-layout[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\n  width: 200px;\n  background-color: #f0f0f0;\n  padding: 20px;\n}\n.admin-layout[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n}\n.admin-layout[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  display: block;\n  padding: 10px 0;\n  text-decoration: none;\n  color: #333;\n}\n.admin-layout[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\n  font-weight: bold;\n  color: #007bff;\n}\n.admin-layout[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  padding: 20px;\n}\n/*# sourceMappingURL=admin-layout.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-admin-layout", standalone: true, imports: [RouterModule], template: '<div class="admin-layout">\n  <aside class="sidebar">\n    <nav>\n      <ul>\n        <li>\n          <a routerLink="users" routerLinkActive="active">User Management</a>\n        </li>\n        <li>\n          <a routerLink="workflow-designer" routerLinkActive="active"\n            >Workflow Designer</a\n          >\n        </li>\n        <!-- Add more admin navigation links here -->\n      </ul>\n    </nav>\n  </aside>\n  <main class="content">\n    <router-outlet></router-outlet>\n  </main>\n</div>\n', styles: ["/* src/app/features/admin/components/admin-layout/admin-layout.component.scss */\n.admin-layout {\n  display: flex;\n  height: 100vh;\n}\n.admin-layout .sidebar {\n  width: 200px;\n  background-color: #f0f0f0;\n  padding: 20px;\n}\n.admin-layout .sidebar nav ul {\n  list-style: none;\n  padding: 0;\n}\n.admin-layout .sidebar nav ul li a {\n  display: block;\n  padding: 10px 0;\n  text-decoration: none;\n  color: #333;\n}\n.admin-layout .sidebar nav ul li a.active {\n  font-weight: bold;\n  color: #007bff;\n}\n.admin-layout .content {\n  flex-grow: 1;\n  padding: 20px;\n}\n/*# sourceMappingURL=admin-layout.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminLayoutComponent, { className: "AdminLayoutComponent", filePath: "src/app/features/admin/components/admin-layout/admin-layout.component.ts", lineNumber: 11 });
})();
export {
  AdminLayoutComponent
};
//# sourceMappingURL=chunk-KX2FQTIY.js.map
