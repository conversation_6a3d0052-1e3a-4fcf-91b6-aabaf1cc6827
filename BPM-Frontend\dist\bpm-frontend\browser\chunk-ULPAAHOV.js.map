{"version": 3, "sources": ["src/app/features/admin/components/workflow-designer/workflow-designer.component.ts", "src/app/features/admin/components/workflow-designer/workflow-designer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-workflow-designer',\n  templateUrl: './workflow-designer.component.html',\n  styleUrls: ['./workflow-designer.component.scss'],\n})\nexport class WorkflowDesignerComponent {}\n", "<p>workflow-designer works!</p>\n"], "mappings": ";;;;;;;;;;;AAOM,IAAO,4BAAP,MAAO,2BAAyB;;qCAAzB,4BAAyB;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACPtC,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,0BAAA;AAAwB,MAAA,uBAAA;;;;;sEDOd,2BAAyB,CAAA;UALrC;uBACW,yBAAuB,UAAA,oCAAA,CAAA;;;;6EAItB,2BAAyB,EAAA,WAAA,6BAAA,UAAA,sFAAA,YAAA,EAAA,CAAA;AAAA,GAAA;", "names": []}