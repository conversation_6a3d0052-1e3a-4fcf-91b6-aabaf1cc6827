import {
  AuthService
} from "./chunk-EDH5VTX4.js";
import {
  <PERSON><PERSON>ard,
  Mat<PERSON>ard<PERSON><PERSON>,
  MatCardAvatar,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardSubtitle,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  Router,
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  NgForOf,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/features/dashboard/components/dashboard/dashboard.component.ts
function DashboardComponent_mat_card_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-card", 4);
    \u0275\u0275listener("click", function DashboardComponent_mat_card_32_Template_mat_card_click_0_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.navigateTo("/dashboard/manager"));
    });
    \u0275\u0275elementStart(1, "mat-card-header")(2, "mat-icon", 5);
    \u0275\u0275text(3, "supervisor_account");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-card-title");
    \u0275\u0275text(5, "Manager Dashboard");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "mat-card-subtitle");
    \u0275\u0275text(7, "Approve and manage team requests");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 6)(10, "div", 7)(11, "span", 8);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 9);
    \u0275\u0275text(14, "Pending Approvals");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 7)(16, "span", 8);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "span", 9);
    \u0275\u0275text(19, "Team Requests");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(20, "mat-card-actions")(21, "button", 10)(22, "mat-icon");
    \u0275\u0275text(23, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(24, " View Details ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(12);
    \u0275\u0275textInterpolate(ctx_r2.managerStats.pendingApprovals);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.managerStats.teamRequests);
  }
}
function DashboardComponent_mat_card_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-card", 4);
    \u0275\u0275listener("click", function DashboardComponent_mat_card_33_Template_mat_card_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.navigateTo("/dashboard/hr"));
    });
    \u0275\u0275elementStart(1, "mat-card-header")(2, "mat-icon", 5);
    \u0275\u0275text(3, "people");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-card-title");
    \u0275\u0275text(5, "HR Dashboard");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "mat-card-subtitle");
    \u0275\u0275text(7, "Process and archive requests");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 6)(10, "div", 7)(11, "span", 8);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 9);
    \u0275\u0275text(14, "To Process");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 7)(16, "span", 8);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "span", 9);
    \u0275\u0275text(19, "Processed Today");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(20, "mat-card-actions")(21, "button", 10)(22, "mat-icon");
    \u0275\u0275text(23, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(24, " View Details ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(12);
    \u0275\u0275textInterpolate(ctx_r2.hrStats.toProcess);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.hrStats.processed);
  }
}
function DashboardComponent_mat_card_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-card", 4);
    \u0275\u0275listener("click", function DashboardComponent_mat_card_34_Template_mat_card_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.navigateTo("/dashboard/reports"));
    });
    \u0275\u0275elementStart(1, "mat-card-header")(2, "mat-icon", 5);
    \u0275\u0275text(3, "analytics");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-card-title");
    \u0275\u0275text(5, "Reports & Analytics");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "mat-card-subtitle");
    \u0275\u0275text(7, "View insights and statistics");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 6)(10, "div", 7)(11, "span", 8);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 9);
    \u0275\u0275text(14, "Total Requests");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 7)(16, "span", 8);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "span", 9);
    \u0275\u0275text(19, "Avg. Processing Time");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(20, "mat-card-actions")(21, "button", 10)(22, "mat-icon");
    \u0275\u0275text(23, "arrow_forward");
    \u0275\u0275elementEnd();
    \u0275\u0275text(24, " View Details ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(12);
    \u0275\u0275textInterpolate(ctx_r2.reportStats.totalRequests);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.reportStats.avgProcessingTime);
  }
}
function DashboardComponent_button_47_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "button", 20)(1, "mat-icon");
    \u0275\u0275text(2, "account_tree");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Manage Workflows ");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_button_48_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "button", 21)(1, "mat-icon");
    \u0275\u0275text(2, "group");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Manage Users ");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_55_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24)(1, "mat-icon", 25);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 26)(4, "div", 27);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 28);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 29);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const activity_r6 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("color", ctx_r2.getActivityColor(activity_r6.type));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r2.getActivityIcon(activity_r6.type));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r6.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(activity_r6.description);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.formatTime(activity_r6.timestamp));
  }
}
function DashboardComponent_div_55_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22);
    \u0275\u0275template(1, DashboardComponent_div_55_div_1_Template, 10, 5, "div", 23);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r2.recentActivities);
  }
}
function DashboardComponent_ng_template_56_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30)(1, "mat-icon");
    \u0275\u0275text(2, "inbox");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "No recent activity");
    \u0275\u0275elementEnd()();
  }
}
var DashboardComponent = class _DashboardComponent {
  authService;
  router;
  destroy$ = new Subject();
  currentUser = null;
  hasManagerAccess = false;
  hasHRAccess = false;
  hasAdminAccess = false;
  hasReportAccess = false;
  employeeStats = {
    pendingRequests: 0,
    completedRequests: 0
  };
  managerStats = {
    pendingApprovals: 0,
    teamRequests: 0
  };
  hrStats = {
    toProcess: 0,
    processed: 0
  };
  reportStats = {
    totalRequests: 0,
    avgProcessingTime: "0h"
  };
  recentActivities = [];
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  ngOnInit() {
    this.loadUserData();
    this.loadDashboardStats();
    this.loadRecentActivity();
    this.redirectToRoleDashboard();
  }
  redirectToRoleDashboard() {
    const dashboardRoute = this.authService.getDashboardRoute();
    if (this.router.url === "/dashboard" || this.router.url === "/dashboard/") {
      this.router.navigate([dashboardRoute]);
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadUserData() {
    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe((user) => {
      this.currentUser = user;
      if (user) {
        this.hasManagerAccess = this.authService.hasAnyRole(["Manager", "Admin"]);
        this.hasHRAccess = this.authService.hasAnyRole(["HR", "Admin"]);
        this.hasAdminAccess = this.authService.hasRole("Admin");
        this.hasReportAccess = this.authService.hasAnyRole(["Manager", "HR", "Admin"]);
      }
    });
  }
  loadDashboardStats() {
    this.employeeStats = {
      pendingRequests: 3,
      completedRequests: 12
    };
    this.managerStats = {
      pendingApprovals: 5,
      teamRequests: 18
    };
    this.hrStats = {
      toProcess: 8,
      processed: 15
    };
    this.reportStats = {
      totalRequests: 156,
      avgProcessingTime: "2.5h"
    };
  }
  loadRecentActivity() {
    this.recentActivities = [
      {
        type: "request_submitted",
        title: "Leave Request Submitted",
        description: "Your annual leave request has been submitted for approval",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1e3)
        // 2 hours ago
      },
      {
        type: "request_approved",
        title: "Expense Report Approved",
        description: "Your expense report #ER-2024-001 has been approved",
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1e3)
        // 5 hours ago
      },
      {
        type: "workflow_updated",
        title: "Workflow Updated",
        description: "The IT Support workflow has been updated",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1e3)
        // 1 day ago
      }
    ];
  }
  navigateTo(route) {
    this.router.navigate([route]);
  }
  getActivityIcon(type) {
    const iconMap = {
      "request_submitted": "send",
      "request_approved": "check_circle",
      "request_rejected": "cancel",
      "workflow_updated": "update",
      "user_assigned": "person_add"
    };
    return iconMap[type] || "info";
  }
  getActivityColor(type) {
    const colorMap = {
      "request_submitted": "primary",
      "request_approved": "primary",
      "request_rejected": "warn",
      "workflow_updated": "accent",
      "user_assigned": "primary"
    };
    return colorMap[type] || "";
  }
  formatTime(timestamp) {
    const now = /* @__PURE__ */ new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1e3 * 60));
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hours ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} days ago`;
    }
  }
  static \u0275fac = function DashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardComponent)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardComponent, selectors: [["app-dashboard"]], decls: 58, vars: 10, consts: [["noActivity", ""], [1, "dashboard-container"], [1, "welcome-section"], [1, "dashboard-grid"], [1, "dashboard-card", 3, "click"], ["mat-card-avatar", ""], [1, "card-stats"], [1, "stat-item"], [1, "stat-number"], [1, "stat-label"], ["mat-button", "", "color", "primary"], ["class", "dashboard-card", 3, "click", 4, "ngIf"], [1, "quick-actions-section"], [1, "actions-grid"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests/new"], ["mat-raised-button", "", "color", "accent", "routerLink", "/requests"], ["mat-raised-button", "", "routerLink", "/workflows", 4, "ngIf"], ["mat-raised-button", "", "routerLink", "/admin/users", 4, "ngIf"], [1, "recent-activity-section"], ["class", "activity-list", 4, "ngIf", "ngIfElse"], ["mat-raised-button", "", "routerLink", "/workflows"], ["mat-raised-button", "", "routerLink", "/admin/users"], [1, "activity-list"], ["class", "activity-item", 4, "ngFor", "ngForOf"], [1, "activity-item"], [3, "color"], [1, "activity-content"], [1, "activity-title"], [1, "activity-description"], [1, "activity-time"], [1, "no-activity"]], template: function DashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "h1");
      \u0275\u0275text(3);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Here's what's happening with your business processes today.");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 3)(7, "mat-card", 4);
      \u0275\u0275listener("click", function DashboardComponent_Template_mat_card_click_7_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.navigateTo("/dashboard/employee"));
      });
      \u0275\u0275elementStart(8, "mat-card-header")(9, "mat-icon", 5);
      \u0275\u0275text(10, "person");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "mat-card-title");
      \u0275\u0275text(12, "My Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "mat-card-subtitle");
      \u0275\u0275text(14, "View your requests and tasks");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(15, "mat-card-content")(16, "div", 6)(17, "div", 7)(18, "span", 8);
      \u0275\u0275text(19);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "span", 9);
      \u0275\u0275text(21, "Pending Requests");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(22, "div", 7)(23, "span", 8);
      \u0275\u0275text(24);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "span", 9);
      \u0275\u0275text(26, "Completed");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(27, "mat-card-actions")(28, "button", 10)(29, "mat-icon");
      \u0275\u0275text(30, "arrow_forward");
      \u0275\u0275elementEnd();
      \u0275\u0275text(31, " View Details ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(32, DashboardComponent_mat_card_32_Template, 25, 2, "mat-card", 11)(33, DashboardComponent_mat_card_33_Template, 25, 2, "mat-card", 11)(34, DashboardComponent_mat_card_34_Template, 25, 2, "mat-card", 11);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "div", 12)(36, "h2");
      \u0275\u0275text(37, "Quick Actions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "div", 13)(39, "button", 14)(40, "mat-icon");
      \u0275\u0275text(41, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(42, " New Request ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(43, "button", 15)(44, "mat-icon");
      \u0275\u0275text(45, "list");
      \u0275\u0275elementEnd();
      \u0275\u0275text(46, " My Requests ");
      \u0275\u0275elementEnd();
      \u0275\u0275template(47, DashboardComponent_button_47_Template, 4, 0, "button", 16)(48, DashboardComponent_button_48_Template, 4, 0, "button", 17);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(49, "div", 18)(50, "mat-card")(51, "mat-card-header")(52, "mat-card-title");
      \u0275\u0275text(53, "Recent Activity");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(54, "mat-card-content");
      \u0275\u0275template(55, DashboardComponent_div_55_Template, 2, 1, "div", 19)(56, DashboardComponent_ng_template_56_Template, 5, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      const noActivity_r7 = \u0275\u0275reference(57);
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate1("Welcome back, ", ctx.currentUser == null ? null : ctx.currentUser.firstName, "!");
      \u0275\u0275advance(16);
      \u0275\u0275textInterpolate(ctx.employeeStats.pendingRequests);
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.employeeStats.completedRequests);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.hasManagerAccess);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasHRAccess);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasReportAccess);
      \u0275\u0275advance(13);
      \u0275\u0275property("ngIf", ctx.hasAdminAccess);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasAdminAccess);
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", ctx.recentActivities.length > 0)("ngIfElse", noActivity_r7);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule, RouterLink, MatCardModule, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardHeader, MatCardSubtitle, MatCardTitle, MatButtonModule, MatButton, MatIconModule, MatIcon], styles: ["\n\n.dashboard-container[_ngcontent-%COMP%] {\n  padding: 0;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.welcome-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  color: #666;\n  margin: 0;\n}\n.dashboard-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n.dashboard-card[_ngcontent-%COMP%] {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.dashboard-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%] {\n  padding-bottom: 1rem;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-icon[mat-card-avatar][_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #333;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n}\n.dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-around;\n  margin: 1rem 0;\n}\n.dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #667eea;\n  line-height: 1;\n}\n.dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 0.8rem;\n  color: #666;\n  margin-top: 0.25rem;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%] {\n  padding-top: 1rem;\n  border-top: 1px solid #f0f0f0;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  color: #667eea;\n  font-weight: 500;\n}\n.dashboard-card[_ngcontent-%COMP%]   mat-card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-left: 0.5rem;\n}\n.quick-actions-section[_ngcontent-%COMP%] {\n  margin-bottom: 3rem;\n}\n.quick-actions-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 1rem;\n}\n.quick-actions-section[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n.quick-actions-section[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  height: 60px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n.quick-actions-section[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n.quick-actions-section[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 0.5rem;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #333;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  padding: 1rem 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  margin-right: 1rem;\n  margin-top: 0.25rem;\n  font-size: 20px;\n  width: 20px;\n  height: 20px;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 0.25rem;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-description[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 0.25rem;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%] {\n  color: #999;\n  font-size: 0.8rem;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activity[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activity[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.recent-activity-section[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]   .no-activity[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1rem;\n}\n@media (max-width: 768px) {\n  .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n  .welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n    font-size: 1rem;\n  }\n  .dashboard-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  .actions-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n    height: 50px;\n    font-size: 0.9rem;\n  }\n}\n@media (max-width: 480px) {\n  .dashboard-container[_ngcontent-%COMP%] {\n    padding: 0 0.5rem;\n  }\n  .welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.75rem;\n  }\n  .dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .dashboard-card[_ngcontent-%COMP%]   .card-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\n    font-size: 0.75rem;\n  }\n}\n.dashboard-card[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\n}\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.dashboard-card[_ngcontent-%COMP%]:nth-child(1) {\n  animation-delay: 0.1s;\n}\n.dashboard-card[_ngcontent-%COMP%]:nth-child(2) {\n  animation-delay: 0.2s;\n}\n.dashboard-card[_ngcontent-%COMP%]:nth-child(3) {\n  animation-delay: 0.3s;\n}\n.dashboard-card[_ngcontent-%COMP%]:nth-child(4) {\n  animation-delay: 0.4s;\n}\n/*# sourceMappingURL=dashboard.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule
    ], template: `
    <div class="dashboard-container">
      <div class="welcome-section">
        <h1>Welcome back, {{currentUser?.firstName}}!</h1>
        <p>Here's what's happening with your business processes today.</p>
      </div>

      <div class="dashboard-grid">
        <!-- Employee Dashboard Card -->
        <mat-card class="dashboard-card" (click)="navigateTo('/dashboard/employee')">
          <mat-card-header>
            <mat-icon mat-card-avatar>person</mat-icon>
            <mat-card-title>My Dashboard</mat-card-title>
            <mat-card-subtitle>View your requests and tasks</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-number">{{employeeStats.pendingRequests}}</span>
                <span class="stat-label">Pending Requests</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{employeeStats.completedRequests}}</span>
                <span class="stat-label">Completed</span>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Details
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Manager Dashboard Card -->
        <mat-card 
          class="dashboard-card" 
          *ngIf="hasManagerAccess"
          (click)="navigateTo('/dashboard/manager')">
          <mat-card-header>
            <mat-icon mat-card-avatar>supervisor_account</mat-icon>
            <mat-card-title>Manager Dashboard</mat-card-title>
            <mat-card-subtitle>Approve and manage team requests</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-number">{{managerStats.pendingApprovals}}</span>
                <span class="stat-label">Pending Approvals</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{managerStats.teamRequests}}</span>
                <span class="stat-label">Team Requests</span>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Details
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- HR Dashboard Card -->
        <mat-card 
          class="dashboard-card" 
          *ngIf="hasHRAccess"
          (click)="navigateTo('/dashboard/hr')">
          <mat-card-header>
            <mat-icon mat-card-avatar>people</mat-icon>
            <mat-card-title>HR Dashboard</mat-card-title>
            <mat-card-subtitle>Process and archive requests</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-number">{{hrStats.toProcess}}</span>
                <span class="stat-label">To Process</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{hrStats.processed}}</span>
                <span class="stat-label">Processed Today</span>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Details
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Reports Dashboard Card -->
        <mat-card 
          class="dashboard-card" 
          *ngIf="hasReportAccess"
          (click)="navigateTo('/dashboard/reports')">
          <mat-card-header>
            <mat-icon mat-card-avatar>analytics</mat-icon>
            <mat-card-title>Reports & Analytics</mat-card-title>
            <mat-card-subtitle>View insights and statistics</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-number">{{reportStats.totalRequests}}</span>
                <span class="stat-label">Total Requests</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{reportStats.avgProcessingTime}}</span>
                <span class="stat-label">Avg. Processing Time</span>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button color="primary">
              <mat-icon>arrow_forward</mat-icon>
              View Details
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Quick Actions Section -->
      <div class="quick-actions-section">
        <h2>Quick Actions</h2>
        <div class="actions-grid">
          <button mat-raised-button color="primary" routerLink="/requests/new">
            <mat-icon>add</mat-icon>
            New Request
          </button>
          <button mat-raised-button color="accent" routerLink="/requests">
            <mat-icon>list</mat-icon>
            My Requests
          </button>
          <button mat-raised-button routerLink="/workflows" *ngIf="hasAdminAccess">
            <mat-icon>account_tree</mat-icon>
            Manage Workflows
          </button>
          <button mat-raised-button routerLink="/admin/users" *ngIf="hasAdminAccess">
            <mat-icon>group</mat-icon>
            Manage Users
          </button>
        </div>
      </div>

      <!-- Recent Activity Section -->
      <div class="recent-activity-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Recent Activity</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list" *ngIf="recentActivities.length > 0; else noActivity">
              <div class="activity-item" *ngFor="let activity of recentActivities">
                <mat-icon [color]="getActivityColor(activity.type)">{{getActivityIcon(activity.type)}}</mat-icon>
                <div class="activity-content">
                  <div class="activity-title">{{activity.title}}</div>
                  <div class="activity-description">{{activity.description}}</div>
                  <div class="activity-time">{{formatTime(activity.timestamp)}}</div>
                </div>
              </div>
            </div>
            <ng-template #noActivity>
              <div class="no-activity">
                <mat-icon>inbox</mat-icon>
                <p>No recent activity</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `, styles: ["/* src/app/features/dashboard/components/dashboard/dashboard.component.scss */\n.dashboard-container {\n  padding: 0;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.welcome-section {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n.welcome-section h1 {\n  font-size: 2.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.welcome-section p {\n  font-size: 1.1rem;\n  color: #666;\n  margin: 0;\n}\n.dashboard-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n.dashboard-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.dashboard-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.dashboard-card mat-card-header {\n  padding-bottom: 1rem;\n}\n.dashboard-card mat-card-header mat-icon[mat-card-avatar] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.dashboard-card mat-card-header mat-card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #333;\n}\n.dashboard-card mat-card-header mat-card-subtitle {\n  color: #666;\n  font-size: 0.9rem;\n}\n.dashboard-card .card-stats {\n  display: flex;\n  justify-content: space-around;\n  margin: 1rem 0;\n}\n.dashboard-card .card-stats .stat-item {\n  text-align: center;\n}\n.dashboard-card .card-stats .stat-item .stat-number {\n  display: block;\n  font-size: 2rem;\n  font-weight: 700;\n  color: #667eea;\n  line-height: 1;\n}\n.dashboard-card .card-stats .stat-item .stat-label {\n  display: block;\n  font-size: 0.8rem;\n  color: #666;\n  margin-top: 0.25rem;\n}\n.dashboard-card mat-card-actions {\n  padding-top: 1rem;\n  border-top: 1px solid #f0f0f0;\n}\n.dashboard-card mat-card-actions button {\n  color: #667eea;\n  font-weight: 500;\n}\n.dashboard-card mat-card-actions button mat-icon {\n  margin-left: 0.5rem;\n}\n.quick-actions-section {\n  margin-bottom: 3rem;\n}\n.quick-actions-section h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 1rem;\n}\n.quick-actions-section .actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n.quick-actions-section .actions-grid button {\n  height: 60px;\n  font-size: 1rem;\n  font-weight: 500;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n.quick-actions-section .actions-grid button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n}\n.quick-actions-section .actions-grid button mat-icon {\n  margin-right: 0.5rem;\n}\n.recent-activity-section mat-card {\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.recent-activity-section mat-card mat-card-header mat-card-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #333;\n}\n.recent-activity-section mat-card .activity-list .activity-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 1rem 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n.recent-activity-section mat-card .activity-list .activity-item:last-child {\n  border-bottom: none;\n}\n.recent-activity-section mat-card .activity-list .activity-item mat-icon {\n  margin-right: 1rem;\n  margin-top: 0.25rem;\n  font-size: 20px;\n  width: 20px;\n  height: 20px;\n}\n.recent-activity-section mat-card .activity-list .activity-item .activity-content {\n  flex: 1;\n}\n.recent-activity-section mat-card .activity-list .activity-item .activity-content .activity-title {\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 0.25rem;\n}\n.recent-activity-section mat-card .activity-list .activity-item .activity-content .activity-description {\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 0.25rem;\n}\n.recent-activity-section mat-card .activity-list .activity-item .activity-content .activity-time {\n  color: #999;\n  font-size: 0.8rem;\n}\n.recent-activity-section mat-card .no-activity {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.recent-activity-section mat-card .no-activity mat-icon {\n  font-size: 48px;\n  width: 48px;\n  height: 48px;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.recent-activity-section mat-card .no-activity p {\n  margin: 0;\n  font-size: 1rem;\n}\n@media (max-width: 768px) {\n  .welcome-section h1 {\n    font-size: 2rem;\n  }\n  .welcome-section p {\n    font-size: 1rem;\n  }\n  .dashboard-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  .actions-grid {\n    grid-template-columns: 1fr;\n  }\n  .actions-grid button {\n    height: 50px;\n    font-size: 0.9rem;\n  }\n}\n@media (max-width: 480px) {\n  .dashboard-container {\n    padding: 0 0.5rem;\n  }\n  .welcome-section h1 {\n    font-size: 1.75rem;\n  }\n  .dashboard-card .card-stats .stat-item .stat-number {\n    font-size: 1.5rem;\n  }\n  .dashboard-card .card-stats .stat-item .stat-label {\n    font-size: 0.75rem;\n  }\n}\n.dashboard-card {\n  animation: fadeInUp 0.6s ease-out;\n}\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.dashboard-card:nth-child(1) {\n  animation-delay: 0.1s;\n}\n.dashboard-card:nth-child(2) {\n  animation-delay: 0.2s;\n}\n.dashboard-card:nth-child(3) {\n  animation-delay: 0.3s;\n}\n.dashboard-card:nth-child(4) {\n  animation-delay: 0.4s;\n}\n/*# sourceMappingURL=dashboard.component.css.map */\n"] }]
  }], () => [{ type: AuthService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardComponent, { className: "DashboardComponent", filePath: "src/app/features/dashboard/components/dashboard/dashboard.component.ts", lineNumber: 201 });
})();
export {
  DashboardComponent
};
//# sourceMappingURL=chunk-LLXPY6UV.js.map
