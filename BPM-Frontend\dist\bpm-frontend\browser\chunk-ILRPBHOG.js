import {
  environment
} from "./chunk-AKJJBQK4.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-RCHKY2RO.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-JTRMQXMJ.js";

// src/app/core/services/workflow.service.ts
var WorkflowService = class _WorkflowService {
  http;
  API_URL = `${environment.apiUrl}/api/Workflow`;
  constructor(http) {
    this.http = http;
  }
  getWorkflows(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.pageNumber)
        httpParams = httpParams.set("pageNumber", params.pageNumber.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.searchTerm)
        httpParams = httpParams.set("searchTerm", params.searchTerm);
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortDirection)
        httpParams = httpParams.set("sortDirection", params.sortDirection);
    }
    return this.http.get(this.API_URL, { params: httpParams });
  }
  getActiveWorkflows() {
    return this.http.get(`${this.API_URL}/active`);
  }
  getWorkflowById(id) {
    return this.http.get(`${this.API_URL}/${id}`);
  }
  getWorkflowWithSteps(id) {
    return this.http.get(`${this.API_URL}/${id}/steps`);
  }
  createWorkflow(workflow) {
    return this.http.post(this.API_URL, workflow);
  }
  updateWorkflow(id, workflow) {
    return this.http.put(`${this.API_URL}/${id}`, workflow);
  }
  deleteWorkflow(id) {
    return this.http.delete(`${this.API_URL}/${id}`);
  }
  // Get workflow steps
  getWorkflowSteps(workflowId) {
    return this.http.get(`${this.API_URL}/${workflowId}/steps`);
  }
  activateWorkflow(id) {
    return this.http.patch(`${this.API_URL}/${id}/activate`, {});
  }
  deactivateWorkflow(id) {
    return this.http.patch(`${this.API_URL}/${id}/deactivate`, {});
  }
  duplicateWorkflow(id) {
    return this.http.post(`${this.API_URL}/${id}/duplicate`, {});
  }
  getWorkflowVersions(workflowName) {
    return this.http.get(`${this.API_URL}/versions/${workflowName}`);
  }
  static \u0275fac = function WorkflowService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _WorkflowService, factory: _WorkflowService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  WorkflowService
};
//# sourceMappingURL=chunk-ILRPBHOG.js.map
