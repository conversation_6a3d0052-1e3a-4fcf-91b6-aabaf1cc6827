{"version": 3, "sources": ["src/app/features/dashboard/components/manager-dashboard/manager-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { Subject, takeUntil } from 'rxjs';\n\nimport { RequestService } from '../../../../core/services/request.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { RequestDto, RequestStatus, RequestType } from '../../../../core/models';\n\ninterface TeamMetrics {\n  totalTeamMembers: number;\n  pendingApprovals: number;\n  approvedThisMonth: number;\n  rejectedThisMonth: number;\n  averageApprovalTime: number;\n}\n\n@Component({\n  selector: 'app-manager-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatChipsModule,\n    MatProgressBarModule,\n    MatTabsModule\n  ],\n  template: `\n    <div class=\"manager-dashboard\">\n      <div class=\"dashboard-header\">\n        <h1>Manager Dashboard</h1>\n        <p>Manage your team's requests and approvals</p>\n      </div>\n\n      <!-- Key Metrics -->\n      <div class=\"metrics-grid\">\n        <mat-card class=\"metric-card pending\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>pending_actions</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{teamMetrics.pendingApprovals}}</h3>\n                <p>Pending Approvals</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card team\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>group</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{teamMetrics.totalTeamMembers}}</h3>\n                <p>Team Members</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card approved\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>check_circle</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{teamMetrics.approvedThisMonth}}</h3>\n                <p>Approved This Month</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"metric-card time\">\n          <mat-card-content>\n            <div class=\"metric\">\n              <div class=\"metric-icon\">\n                <mat-icon>schedule</mat-icon>\n              </div>\n              <div class=\"metric-info\">\n                <h3>{{teamMetrics.averageApprovalTime}}h</h3>\n                <p>Avg. Approval Time</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n\n      <!-- Main Content Tabs -->\n      <mat-card class=\"content-card\">\n        <mat-tab-group>\n          <!-- Pending Approvals -->\n          <mat-tab label=\"Pending Approvals ({{pendingRequests.length}})\">\n            <div class=\"tab-content\">\n              <div class=\"tab-header\">\n                <h3>Requests Awaiting Your Approval</h3>\n                <button mat-raised-button color=\"primary\" routerLink=\"/requests/approval\">\n                  <mat-icon>approval</mat-icon>\n                  View All Approvals\n                </button>\n              </div>\n\n              <div *ngIf=\"pendingRequests.length > 0\" class=\"requests-table\">\n                <table mat-table [dataSource]=\"pendingRequests\">\n                  <!-- Employee Column -->\n                  <ng-container matColumnDef=\"employee\">\n                    <th mat-header-cell *matHeaderCellDef>Employee</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"employee-info\">\n                        <strong>{{request.initiatorName}}</strong>\n                        <small>{{request.createdAt | date:'short'}}</small>\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <!-- Request Type Column -->\n                  <ng-container matColumnDef=\"type\">\n                    <th mat-header-cell *matHeaderCellDef>Type</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <mat-chip [class]=\"getRequestTypeClass(request.type)\">\n                        {{getRequestTypeLabel(request.type)}}\n                      </mat-chip>\n                    </td>\n                  </ng-container>\n\n                  <!-- Title Column -->\n                  <ng-container matColumnDef=\"title\">\n                    <th mat-header-cell *matHeaderCellDef>Request</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"request-title\">\n                        {{request.title || 'No Title'}}\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <!-- Priority Column -->\n                  <ng-container matColumnDef=\"priority\">\n                    <th mat-header-cell *matHeaderCellDef>Priority</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <mat-chip [class]=\"getPriorityClass(request)\">\n                        {{getPriorityLabel(request)}}\n                      </mat-chip>\n                    </td>\n                  </ng-container>\n\n                  <!-- Actions Column -->\n                  <ng-container matColumnDef=\"actions\">\n                    <th mat-header-cell *matHeaderCellDef>Actions</th>\n                    <td mat-cell *matCellDef=\"let request\">\n                      <div class=\"action-buttons\">\n                        <button mat-icon-button [routerLink]=\"['/requests/details', request.id]\">\n                          <mat-icon>visibility</mat-icon>\n                        </button>\n                        <button mat-raised-button color=\"primary\" (click)=\"quickApprove(request)\">\n                          <mat-icon>check</mat-icon>\n                          Approve\n                        </button>\n                        <button mat-raised-button color=\"warn\" (click)=\"quickReject(request)\">\n                          <mat-icon>close</mat-icon>\n                          Reject\n                        </button>\n                      </div>\n                    </td>\n                  </ng-container>\n\n                  <tr mat-header-row *matHeaderRowDef=\"pendingColumns\"></tr>\n                  <tr mat-row *matRowDef=\"let row; columns: pendingColumns;\"></tr>\n                </table>\n              </div>\n\n              <div *ngIf=\"pendingRequests.length === 0\" class=\"no-data\">\n                <mat-icon>assignment_turned_in</mat-icon>\n                <h3>No Pending Approvals</h3>\n                <p>All requests from your team have been processed.</p>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Team Overview -->\n          <mat-tab label=\"Team Overview\">\n            <div class=\"tab-content\">\n              <div class=\"team-stats\">\n                <div class=\"stat-card\">\n                  <h4>Request Volume</h4>\n                  <div class=\"stat-chart\">\n                    <div class=\"chart-placeholder\">\n                      <mat-icon>bar_chart</mat-icon>\n                      <p>Request volume chart would be displayed here</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"stat-card\">\n                  <h4>Approval Trends</h4>\n                  <div class=\"approval-breakdown\">\n                    <div class=\"breakdown-item\">\n                      <span class=\"label\">Approved:</span>\n                      <span class=\"value\">{{teamMetrics.approvedThisMonth}}</span>\n                      <mat-progress-bar mode=\"determinate\" [value]=\"getApprovalPercentage()\"></mat-progress-bar>\n                    </div>\n                    <div class=\"breakdown-item\">\n                      <span class=\"label\">Rejected:</span>\n                      <span class=\"value\">{{teamMetrics.rejectedThisMonth}}</span>\n                      <mat-progress-bar mode=\"determinate\" [value]=\"getRejectionPercentage()\"></mat-progress-bar>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Recent Activity -->\n          <mat-tab label=\"Recent Activity\">\n            <div class=\"tab-content\">\n              <div class=\"activity-list\">\n                <div *ngFor=\"let activity of recentActivities\" class=\"activity-item\">\n                  <div class=\"activity-icon\">\n                    <mat-icon>{{activity.icon}}</mat-icon>\n                  </div>\n                  <div class=\"activity-content\">\n                    <p>{{activity.description}}</p>\n                    <small>{{activity.timestamp | date:'short'}}</small>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n        </mat-tab-group>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .manager-dashboard {\n      padding: 1rem;\n      max-width: 1400px;\n      margin: 0 auto;\n    }\n\n    .dashboard-header {\n      margin-bottom: 2rem;\n    }\n\n    .dashboard-header h1 {\n      margin: 0;\n      color: #333;\n    }\n\n    .dashboard-header p {\n      margin: 0.5rem 0 0 0;\n      color: #666;\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .metric-card {\n      transition: transform 0.2s ease;\n    }\n\n    .metric-card:hover {\n      transform: translateY(-2px);\n    }\n\n    .metric-card.pending {\n      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);\n      color: white;\n    }\n\n    .metric-card.team {\n      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);\n      color: white;\n    }\n\n    .metric-card.approved {\n      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);\n      color: white;\n    }\n\n    .metric-card.time {\n      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);\n      color: white;\n    }\n\n    .metric {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .metric-icon mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n    }\n\n    .metric-info h3 {\n      margin: 0;\n      font-size: 2rem;\n      font-weight: bold;\n    }\n\n    .metric-info p {\n      margin: 0.25rem 0 0 0;\n      font-size: 1rem;\n    }\n\n    .content-card {\n      margin-top: 1rem;\n    }\n\n    .tab-content {\n      padding: 1rem;\n    }\n\n    .tab-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .requests-table {\n      overflow-x: auto;\n    }\n\n    .requests-table table {\n      width: 100%;\n    }\n\n    .employee-info {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .employee-info small {\n      color: #666;\n      font-size: 0.8rem;\n    }\n\n    .request-title {\n      max-width: 200px;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 0.5rem;\n      align-items: center;\n    }\n\n    .type-leave { background-color: #e3f2fd; color: #1976d2; }\n    .type-expense { background-color: #f3e5f5; color: #7b1fa2; }\n    .type-training { background-color: #e8f5e8; color: #2e7d32; }\n    .type-it { background-color: #fff3e0; color: #ef6c00; }\n    .type-profile { background-color: #fce4ec; color: #c2185b; }\n\n    .priority-high { background-color: #ffebee; color: #c62828; }\n    .priority-medium { background-color: #fff3e0; color: #ef6c00; }\n    .priority-low { background-color: #e8f5e8; color: #2e7d32; }\n\n    .no-data {\n      text-align: center;\n      padding: 3rem;\n      color: #666;\n    }\n\n    .no-data mat-icon {\n      font-size: 4rem;\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      color: #4caf50;\n    }\n\n    .team-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 2rem;\n    }\n\n    .stat-card {\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .stat-card h4 {\n      margin: 0 0 1rem 0;\n      color: #333;\n    }\n\n    .chart-placeholder {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .chart-placeholder mat-icon {\n      font-size: 3rem;\n      width: 3rem;\n      height: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    .approval-breakdown {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .breakdown-item {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n    }\n\n    .breakdown-item .label {\n      font-weight: 500;\n    }\n\n    .activity-list {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .activity-item {\n      display: flex;\n      align-items: center;\n      gap: 1rem;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .activity-icon {\n      width: 40px;\n      height: 40px;\n      border-radius: 50%;\n      background-color: #2196f3;\n      color: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .activity-content p {\n      margin: 0;\n      font-weight: 500;\n    }\n\n    .activity-content small {\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .metrics-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .tab-header {\n        flex-direction: column;\n        align-items: stretch;\n        gap: 1rem;\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n\n      .team-stats {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class ManagerDashboardComponent implements OnInit, OnDestroy {\n  private readonly destroy$ = new Subject<void>();\n\n  teamMetrics: TeamMetrics = {\n    totalTeamMembers: 8,\n    pendingApprovals: 5,\n    approvedThisMonth: 23,\n    rejectedThisMonth: 3,\n    averageApprovalTime: 4.2\n  };\n\n  pendingRequests: RequestDto[] = [];\n  pendingColumns: string[] = ['employee', 'type', 'title', 'priority', 'actions'];\n\n  recentActivities = [\n    {\n      icon: 'check_circle',\n      description: 'Approved leave request from John Doe',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago\n    },\n    {\n      icon: 'assignment',\n      description: 'New expense report submitted by Jane Smith',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4 hours ago\n    },\n    {\n      icon: 'cancel',\n      description: 'Rejected training request from Mike Johnson',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6 hours ago\n    }\n  ];\n\n  constructor(\n    private readonly requestService: RequestService,\n    private readonly authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPendingRequests();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadPendingRequests(): void {\n    // Mock data for demonstration\n    this.pendingRequests = [\n      {\n        id: '1',\n        type: RequestType.Leave,\n        initiatorId: 'user1',\n        initiatorName: 'John Doe',\n        status: RequestStatus.Pending,\n        title: 'Vacation Leave - Dec 20-30',\n        description: 'Annual vacation leave',\n        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        isDeleted: false,\n        requestSteps: []\n      },\n      {\n        id: '2',\n        type: RequestType.Expense,\n        initiatorId: 'user2',\n        initiatorName: 'Jane Smith',\n        status: RequestStatus.Pending,\n        title: 'Business Trip Expenses',\n        description: 'Client meeting expenses',\n        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),\n        isDeleted: false,\n        requestSteps: []\n      }\n    ];\n  }\n\n  getRequestTypeLabel(type: RequestType): string {\n    switch (type) {\n      case RequestType.Leave: return 'Leave';\n      case RequestType.Expense: return 'Expense';\n      case RequestType.Training: return 'Training';\n      case RequestType.ITSupport: return 'IT Support';\n      case RequestType.ProfileUpdate: return 'Profile';\n      default: return 'Unknown';\n    }\n  }\n\n  getRequestTypeClass(type: RequestType): string {\n    switch (type) {\n      case RequestType.Leave: return 'type-leave';\n      case RequestType.Expense: return 'type-expense';\n      case RequestType.Training: return 'type-training';\n      case RequestType.ITSupport: return 'type-it';\n      case RequestType.ProfileUpdate: return 'type-profile';\n      default: return '';\n    }\n  }\n\n  getPriorityLabel(request: RequestDto): string {\n    // Calculate priority based on age of request\n    const daysSinceCreated = Math.floor((Date.now() - new Date(request.createdAt).getTime()) / (1000 * 60 * 60 * 24));\n    if (daysSinceCreated > 3) return 'High';\n    if (daysSinceCreated > 1) return 'Medium';\n    return 'Low';\n  }\n\n  getPriorityClass(request: RequestDto): string {\n    const priority = this.getPriorityLabel(request);\n    switch (priority) {\n      case 'High': return 'priority-high';\n      case 'Medium': return 'priority-medium';\n      case 'Low': return 'priority-low';\n      default: return '';\n    }\n  }\n\n  quickApprove(request: RequestDto): void {\n    console.log('Quick approve:', request.id);\n    // In a real app, call the approval service\n  }\n\n  quickReject(request: RequestDto): void {\n    console.log('Quick reject:', request.id);\n    // In a real app, call the rejection service\n  }\n\n  getApprovalPercentage(): number {\n    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;\n    if (total === 0) return 0;\n    return (this.teamMetrics.approvedThisMonth / total) * 100;\n  }\n\n  getRejectionPercentage(): number {\n    const total = this.teamMetrics.approvedThisMonth + this.teamMetrics.rejectedThisMonth;\n    if (total === 0) return 0;\n    return (this.teamMetrics.rejectedThisMonth / total) * 100;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0HoB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;;;;;AAC9C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA,EACV,GAAA,QAAA;AACjB,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;;AAAoC,IAAA,uBAAA,EAAQ,EAC/C;;;;AAFI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,aAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,WAAA,WAAA,OAAA,CAAA;;;;;AAOX,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,UAAA;AAEnC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAW;;;;;AAFD,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,oBAAA,WAAA,IAAA,CAAA;AACR,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,oBAAA,WAAA,IAAA,GAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA;AAEnC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM;;;;AADJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,SAAA,YAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;;;;;AAC9C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,UAAA;AAEnC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAW;;;;;AAFD,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,iBAAA,UAAA,CAAA;AACR,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,iBAAA,UAAA,GAAA,GAAA;;;;;AAOJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuC,GAAA,OAAA,EAAA,EACT,GAAA,UAAA,EAAA,EAC+C,GAAA,UAAA;AAC7D,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;AAEjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0C,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,UAAA,CAAqB;IAAA,CAAA;AACtE,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACf,IAAA,iBAAA,GAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAuC,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,UAAA,CAAoB;IAAA,CAAA;AAClE,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACf,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAXoB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,0BAAA,GAAA,KAAA,WAAA,EAAA,CAAA;;;;;AAe9B,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AAhEJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+D,GAAA,SAAA,EAAA;AAG3D,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;;AAUxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA;;AASxC,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,iDAAA,IAAA,GAAA,MAAA,EAAA;;AAkBxC,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA,EAAqD,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA;AAEvD,IAAA,uBAAA,EAAQ;;;;AAhES,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,OAAA,eAAA;AA8DK,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,cAAA;AACa,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,cAAA;;;;;AAIrC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,UAAA;AAC9C,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AAC9B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,kDAAA;AAAgD,IAAA,uBAAA,EAAI;;;;;AA0CvD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,OAAA,EAAA,EACxC,GAAA,UAAA;AACf,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA,EAAW;AAExC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8B,GAAA,GAAA;AACzB,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,OAAA;AAAO,IAAA,iBAAA,CAAA;;AAAqC,IAAA,uBAAA,EAAQ,EAChD;;;;AALM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,IAAA;AAGP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,WAAA;AACI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,YAAA,WAAA,OAAA,CAAA;;;AAgRrB,IAAO,4BAAP,MAAO,2BAAyB;EAiCjB;EACA;EAjCF,WAAW,IAAI,QAAO;EAEvC,cAA2B;IACzB,kBAAkB;IAClB,kBAAkB;IAClB,mBAAmB;IACnB,mBAAmB;IACnB,qBAAqB;;EAGvB,kBAAgC,CAAA;EAChC,iBAA2B,CAAC,YAAY,QAAQ,SAAS,YAAY,SAAS;EAE9E,mBAAmB;IACjB;MACE,MAAM;MACN,aAAa;MACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;;;IAErD;MACE,MAAM;MACN,aAAa;MACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;;;IAErD;MACE,MAAM;MACN,aAAa;MACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;;;;EAIvD,YACmB,gBACA,aAAwB;AADxB,SAAA,iBAAA;AACA,SAAA,cAAA;EAChB;EAEH,WAAQ;AACN,SAAK,oBAAmB;EAC1B;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEA,sBAAmB;AAEjB,SAAK,kBAAkB;MACrB;QACE,IAAI;QACJ,MAAM,YAAY;QAClB,aAAa;QACb,eAAe;QACf,QAAQ,cAAc;QACtB,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;QACpD,WAAW;QACX,cAAc,CAAA;;MAEhB;QACE,IAAI;QACJ,MAAM,YAAY;QAClB,aAAa;QACb,eAAe;QACf,QAAQ,cAAc;QACtB,OAAO;QACP,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,KAAK,KAAK,GAAI;QACpD,WAAW;QACX,cAAc,CAAA;;;EAGpB;EAEA,oBAAoB,MAAiB;AACnC,YAAQ,MAAM;MACZ,KAAK,YAAY;AAAO,eAAO;MAC/B,KAAK,YAAY;AAAS,eAAO;MACjC,KAAK,YAAY;AAAU,eAAO;MAClC,KAAK,YAAY;AAAW,eAAO;MACnC,KAAK,YAAY;AAAe,eAAO;MACvC;AAAS,eAAO;IAClB;EACF;EAEA,oBAAoB,MAAiB;AACnC,YAAQ,MAAM;MACZ,KAAK,YAAY;AAAO,eAAO;MAC/B,KAAK,YAAY;AAAS,eAAO;MACjC,KAAK,YAAY;AAAU,eAAO;MAClC,KAAK,YAAY;AAAW,eAAO;MACnC,KAAK,YAAY;AAAe,eAAO;MACvC;AAAS,eAAO;IAClB;EACF;EAEA,iBAAiB,SAAmB;AAElC,UAAM,mBAAmB,KAAK,OAAO,KAAK,IAAG,IAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAO,MAAO,MAAO,KAAK,KAAK,GAAG;AAChH,QAAI,mBAAmB;AAAG,aAAO;AACjC,QAAI,mBAAmB;AAAG,aAAO;AACjC,WAAO;EACT;EAEA,iBAAiB,SAAmB;AAClC,UAAM,WAAW,KAAK,iBAAiB,OAAO;AAC9C,YAAQ,UAAU;MAChB,KAAK;AAAQ,eAAO;MACpB,KAAK;AAAU,eAAO;MACtB,KAAK;AAAO,eAAO;MACnB;AAAS,eAAO;IAClB;EACF;EAEA,aAAa,SAAmB;AAC9B,YAAQ,IAAI,kBAAkB,QAAQ,EAAE;EAE1C;EAEA,YAAY,SAAmB;AAC7B,YAAQ,IAAI,iBAAiB,QAAQ,EAAE;EAEzC;EAEA,wBAAqB;AACnB,UAAM,QAAQ,KAAK,YAAY,oBAAoB,KAAK,YAAY;AACpE,QAAI,UAAU;AAAG,aAAO;AACxB,WAAQ,KAAK,YAAY,oBAAoB,QAAS;EACxD;EAEA,yBAAsB;AACpB,UAAM,QAAQ,KAAK,YAAY,oBAAoB,KAAK,YAAY;AACpE,QAAI,UAAU;AAAG,aAAO;AACxB,WAAQ,KAAK,YAAY,oBAAoB,QAAS;EACxD;;qCAxIW,4BAAyB,4BAAA,cAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,MAAA,GAAA,CAAA,GAAA,eAAA,UAAA,GAAA,CAAA,GAAA,eAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,oBAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,eAAA,GAAA,OAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,aAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,UAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,CAAA,gBAAA,UAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,YAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,GAAA,OAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,QAAA,GAAA,OAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAtdlC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EACC,GAAA,IAAA;AACxB,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,2CAAA;AAAyC,MAAA,uBAAA,EAAI;AAIlD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,YAAA,CAAA,EACc,GAAA,kBAAA,EAClB,GAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAW;AAEtC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAgC,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA,EAAI,EACpB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAmC,IAAA,kBAAA,EACf,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAW;AAE5B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAgC,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA,EAAI,EACf,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAuC,IAAA,kBAAA,EACnB,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA,EAAW;AAEnC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAiC,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA,EAAI,EACtB,EACF,EACW;AAGrB,MAAA,yBAAA,IAAA,YAAA,CAAA,EAAmC,IAAA,kBAAA,EACf,IAAA,OAAA,CAAA,EACI,IAAA,OAAA,CAAA,EACO,IAAA,UAAA;AACb,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAW;AAE/B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,EAAA;AAAoC,MAAA,uBAAA;AACxC,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAI,EACrB,EACF,EACW,EACV;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA+B,IAAA,eAAA,EACd,IAAA,WAAA,EAAA,EAEmD,IAAA,OAAA,EAAA,EACrC,IAAA,OAAA,EAAA,EACC,IAAA,IAAA;AAClB,MAAA,iBAAA,IAAA,iCAAA;AAA+B,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAA0E,IAAA,UAAA;AAC9D,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AAClB,MAAA,iBAAA,IAAA,sBAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,qBAAA,IAAA,2CAAA,IAAA,GAAA,OAAA,EAAA,EAA+D,IAAA,2CAAA,GAAA,GAAA,OAAA,EAAA;AAyEjE,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAA+B,IAAA,OAAA,EAAA,EACJ,IAAA,OAAA,EAAA,EACC,IAAA,OAAA,EAAA,EACC,IAAA,IAAA;AACjB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAClB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA,EAAA,EACS,IAAA,UAAA;AACnB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnB,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAI,EAC/C,EACF;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACnB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAgC,IAAA,OAAA,EAAA,EACF,IAAA,QAAA,EAAA;AACN,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAoB,MAAA,iBAAA,EAAA;AAAiC,MAAA,uBAAA;AACrD,MAAA,oBAAA,IAAA,oBAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,QAAA,EAAA;AACN,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAoB,MAAA,iBAAA,EAAA;AAAiC,MAAA,uBAAA;AACrD,MAAA,oBAAA,IAAA,oBAAA,EAAA;AACF,MAAA,uBAAA,EAAM,EACF,EACF,EACF,EACF;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAiC,IAAA,OAAA,EAAA,EACN,IAAA,OAAA,EAAA;AAErB,MAAA,qBAAA,IAAA,2CAAA,IAAA,GAAA,OAAA,EAAA;AASF,MAAA,uBAAA,EAAM,EACF,EACE,EACI,EACP;;;AA9LG,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,gBAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,gBAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,iBAAA;AAcA,MAAA,oBAAA,EAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,YAAA,qBAAA,GAAA;AAYD,MAAA,oBAAA,CAAA;AAAA,MAAA,iCAAA,SAAA,uBAAA,IAAA,gBAAA,QAAA,GAAA;AAUC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,SAAA,CAAA;AAoEA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,WAAA,CAAA;AA2BsB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,iBAAA;AACiB,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,sBAAA,CAAA;AAIjB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,iBAAA;AACiB,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,uBAAA,CAAA;AAYjB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,gBAAA;;oBA3MtC,cAAY,SAAA,MAAA,UACZ,cAAY,YACZ,eAAa,SAAA,gBACb,iBAAe,WAAA,eACf,eAAa,SACb,gBAAc,UAAA,kBAAA,iBAAA,cAAA,YAAA,WAAA,eAAA,SAAA,cAAA,QACd,gBAAc,SACd,sBAAoB,gBACpB,eAAa,QAAA,WAAA,GAAA,QAAA,CAAA,6mLAAA,EAAA,CAAA;;;sEAydJ,2BAAyB,CAAA;UArerC;uBACW,yBAAuB,YACrB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgNT,QAAA,CAAA,0mJAAA,EAAA,CAAA;;;;6EAuQU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,0FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}