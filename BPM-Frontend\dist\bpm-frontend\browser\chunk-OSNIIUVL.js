import {
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule
} from "./chunk-RCHKY2RO.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-JTRMQXMJ.js";

// src/app/features/requests/requests-routing.module.ts
var routes = [
  {
    path: "",
    loadComponent: () => import("./chunk-FHFXZCVY.js").then((c) => c.RequestListComponent)
  },
  {
    path: "new/:type",
    loadComponent: () => import("./chunk-DQ4S732Z.js").then((c) => c.RequestFormComponent)
  },
  {
    path: "details/:id",
    loadComponent: () => import("./chunk-SQGK6BT7.js").then((c) => c.RequestDetailsComponent)
  },
  {
    path: "approval",
    loadComponent: () => import("./chunk-TQQERIZX.js").then((c) => c.RequestApprovalComponent)
  },
  {
    path: "**",
    loadComponent: () => import("./chunk-DLSF3URO.js").then((c) => c.RequestsNotFoundComponent)
  }
];
var RequestsRoutingModule = class _RequestsRoutingModule {
  static \u0275fac = function RequestsRoutingModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestsRoutingModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _RequestsRoutingModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [RouterModule.forChild(routes), RouterModule] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestsRoutingModule, [{
    type: NgModule,
    args: [{
      imports: [RouterModule.forChild(routes)],
      exports: [RouterModule]
    }]
  }], null, null);
})();

// src/app/features/requests/requests.module.ts
var RequestsModule = class _RequestsModule {
  static \u0275fac = function RequestsModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestsModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({ type: _RequestsModule });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({ imports: [
    CommonModule,
    RouterModule,
    RequestsRoutingModule
  ] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestsModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [
        CommonModule,
        RouterModule,
        RequestsRoutingModule
      ]
    }]
  }], null, null);
})();
export {
  RequestsModule
};
//# sourceMappingURL=chunk-OSNIIUVL.js.map
