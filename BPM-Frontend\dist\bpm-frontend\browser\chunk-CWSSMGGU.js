import {
  MatListModule,
  NotificationService
} from "./chunk-TBF62RP6.js";
import "./chunk-7XGPZDLL.js";
import "./chunk-XSFMJNUM.js";
import "./chunk-3EIBYAGU.js";
import "./chunk-W6VU2H2S.js";
import "./chunk-CPP3G34D.js";
import "./chunk-XJ5TS5V6.js";
import "./chunk-D2SQUTFC.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCardModule
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  CommonModule,
  DatePipe,
  NgForOf,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-JTRMQXMJ.js";

// src/app/shared/components/notification-list/notification-list.component.ts
function NotificationListComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4)(1, "p");
    \u0275\u0275text(2, "No new notifications.");
    \u0275\u0275elementEnd()();
  }
}
function NotificationListComponent_ul_4_li_1_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 10);
    \u0275\u0275listener("click", function NotificationListComponent_ul_4_li_1_button_9_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const notification_r2 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.markAsRead(notification_r2));
    });
    \u0275\u0275text(1, " Mark as Read ");
    \u0275\u0275elementEnd();
  }
}
function NotificationListComponent_ul_4_li_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "li")(1, "div", 7)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 8);
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(9, NotificationListComponent_ul_4_li_1_button_9_Template, 2, 0, "button", 9);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const notification_r2 = ctx.$implicit;
    \u0275\u0275classProp("read", notification_r2.isRead);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(notification_r2.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(notification_r2.message);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(8, 6, notification_r2.createdAt, "short"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !notification_r2.isRead);
  }
}
function NotificationListComponent_ul_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ul", 5);
    \u0275\u0275template(1, NotificationListComponent_ul_4_li_1_Template, 10, 9, "li", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r2.notifications);
  }
}
function NotificationListComponent_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 11);
    \u0275\u0275listener("click", function NotificationListComponent_button_5_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.clearAllNotifications());
    });
    \u0275\u0275text(1, " Clear All Notifications ");
    \u0275\u0275elementEnd();
  }
}
var NotificationListComponent = class _NotificationListComponent {
  notificationService;
  notifications = [];
  constructor(notificationService) {
    this.notificationService = notificationService;
  }
  ngOnInit() {
    this.notificationService.getNotifications({ pageNumber: 1, pageSize: 50 }).subscribe((response) => {
      this.notifications = response.data;
    });
  }
  markAsRead(notification) {
    this.notificationService.markAsRead(notification.id).subscribe(() => {
      notification.isRead = true;
    });
  }
  clearAllNotifications() {
    this.notificationService.clearAllNotifications().subscribe(() => {
      this.notifications = [];
    });
  }
  static \u0275fac = function NotificationListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotificationListComponent)(\u0275\u0275directiveInject(NotificationService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotificationListComponent, selectors: [["app-notification-list"]], decls: 6, vars: 3, consts: [[1, "notification-list-container"], ["class", "no-notifications", 4, "ngIf"], ["class", "notification-items", 4, "ngIf"], ["class", "clear-all-button", 3, "click", 4, "ngIf"], [1, "no-notifications"], [1, "notification-items"], [3, "read", 4, "ngFor", "ngForOf"], [1, "notification-content"], [1, "timestamp"], [3, "click", 4, "ngIf"], [3, "click"], [1, "clear-all-button", 3, "click"]], template: function NotificationListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h2");
      \u0275\u0275text(2, "Notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275template(3, NotificationListComponent_div_3_Template, 3, 0, "div", 1)(4, NotificationListComponent_ul_4_Template, 2, 1, "ul", 2)(5, NotificationListComponent_button_5_Template, 2, 0, "button", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.notifications.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.notifications.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.notifications.length > 0);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    DatePipe,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatListModule
  ], styles: ["\n\n.notification-list-container[_ngcontent-%COMP%] {\n  max-width: 500px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-family: Arial, sans-serif;\n}\n.notification-list-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #333;\n  margin-bottom: 20px;\n}\n.notification-list-container[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #777;\n  padding: 20px;\n  border: 1px dashed #ccc;\n  border-radius: 5px;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%] {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  margin-bottom: 10px;\n  background-color: #f9f9f9;\n  border: 1px solid #eee;\n  border-radius: 5px;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li.read[_ngcontent-%COMP%] {\n  background-color: #e9e9e9;\n  color: #999;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li.read[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  text-decoration: line-through;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%] {\n  flex-grow: 1;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1em;\n  line-height: 1.4;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\n  font-size: 0.8em;\n  color: #a0a0a0;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  background-color: #007bff;\n  color: white;\n  border: none;\n  padding: 8px 12px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container[_ngcontent-%COMP%]   .notification-items[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\n  background-color: #0056b3;\n}\n.notification-list-container[_ngcontent-%COMP%]   .clear-all-button[_ngcontent-%COMP%] {\n  display: block;\n  width: 100%;\n  padding: 10px;\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 1em;\n  cursor: pointer;\n  margin-top: 20px;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container[_ngcontent-%COMP%]   .clear-all-button[_ngcontent-%COMP%]:hover {\n  background-color: #c82333;\n}\n/*# sourceMappingURL=notification-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationListComponent, [{
    type: Component,
    args: [{ selector: "app-notification-list", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatListModule
    ], template: '<div class="notification-list-container">\n  <h2>Notifications</h2>\n\n  <div *ngIf="notifications.length === 0" class="no-notifications">\n    <p>No new notifications.</p>\n  </div>\n\n  <ul *ngIf="notifications.length > 0" class="notification-items">\n    <li\n      *ngFor="let notification of notifications"\n      [class.read]="notification.isRead"\n    >\n      <div class="notification-content">\n        <h4>{{ notification.title }}</h4>\n        <p>{{ notification.message }}</p>\n        <span class="timestamp">{{\n          notification.createdAt | date : "short"\n        }}</span>\n      </div>\n      <button *ngIf="!notification.isRead" (click)="markAsRead(notification)">\n        Mark as Read\n      </button>\n    </li>\n  </ul>\n\n  <button\n    *ngIf="notifications.length > 0"\n    (click)="clearAllNotifications()"\n    class="clear-all-button"\n  >\n    Clear All Notifications\n  </button>\n</div>', styles: ["/* src/app/shared/components/notification-list/notification-list.component.scss */\n.notification-list-container {\n  max-width: 500px;\n  margin: 20px auto;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  font-family: Arial, sans-serif;\n}\n.notification-list-container h2 {\n  text-align: center;\n  color: #333;\n  margin-bottom: 20px;\n}\n.notification-list-container .no-notifications {\n  text-align: center;\n  color: #777;\n  padding: 20px;\n  border: 1px dashed #ccc;\n  border-radius: 5px;\n}\n.notification-list-container .notification-items {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n.notification-list-container .notification-items li {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  margin-bottom: 10px;\n  background-color: #f9f9f9;\n  border: 1px solid #eee;\n  border-radius: 5px;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container .notification-items li.read {\n  background-color: #e9e9e9;\n  color: #999;\n}\n.notification-list-container .notification-items li.read .notification-content p {\n  text-decoration: line-through;\n}\n.notification-list-container .notification-items li .notification-content {\n  flex-grow: 1;\n}\n.notification-list-container .notification-items li .notification-content p {\n  margin: 0;\n  font-size: 1em;\n  line-height: 1.4;\n}\n.notification-list-container .notification-items li .notification-content .timestamp {\n  font-size: 0.8em;\n  color: #a0a0a0;\n}\n.notification-list-container .notification-items li button {\n  background-color: #007bff;\n  color: white;\n  border: none;\n  padding: 8px 12px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container .notification-items li button:hover {\n  background-color: #0056b3;\n}\n.notification-list-container .clear-all-button {\n  display: block;\n  width: 100%;\n  padding: 10px;\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 1em;\n  cursor: pointer;\n  margin-top: 20px;\n  transition: background-color 0.3s ease;\n}\n.notification-list-container .clear-all-button:hover {\n  background-color: #c82333;\n}\n/*# sourceMappingURL=notification-list.component.css.map */\n"] }]
  }], () => [{ type: NotificationService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotificationListComponent, { className: "NotificationListComponent", filePath: "src/app/shared/components/notification-list/notification-list.component.ts", lineNumber: 23 });
})();
export {
  NotificationListComponent
};
//# sourceMappingURL=chunk-CWSSMGGU.js.map
