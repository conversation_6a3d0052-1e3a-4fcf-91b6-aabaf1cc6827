import {
  Mat<PERSON>aginator,
  MatPaginatorModule
} from "./chunk-MZM46CDX.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-43VBYOSE.js";
import "./chunk-N7MY3IFD.js";
import "./chunk-XSFMJNUM.js";
import {
  MatFormFieldModule,
  MatInput,
  MatInputModule
} from "./chunk-DKEYCCVZ.js";
import "./chunk-W6VU2H2S.js";
import "./chunk-CPP3G34D.js";
import {
  RequestService
} from "./chunk-WTRFNDWH.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-DALMYUGW.js";
import {
  RequestStatus,
  RequestType
} from "./chunk-XJ5TS5V6.js";
import {
  MatFormField,
  MatLabel,
  MatSuffix
} from "./chunk-R3ISBMK2.js";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-D2SQUTFC.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-L52YVPND.js";
import {
  AuthService
} from "./chunk-EDH5VTX4.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
  MatTableModule
} from "./chunk-PJTOMNHB.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule,
  MatIconButton
} from "./chunk-5XTAYFTV.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  DatePipe,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Subject,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-JTRMQXMJ.js";

// src/app/features/requests/components/request-list/request-list.component.ts
var _c0 = (a0) => ["/requests/details", a0];
var _c1 = () => [5, 10, 25, 50];
function RequestListComponent_div_50_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 13);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_th_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "ID");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_td_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "span", 28);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r1 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(request_r1.id.substring(0, 8));
  }
}
function RequestListComponent_div_51_th_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Title");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_td_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "div", 29)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const request_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(request_r2.title || "No Title");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.getRequestTypeLabel(request_r2.type));
  }
}
function RequestListComponent_div_51_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Status");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const request_r4 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r2.getStatusClass(request_r4.status));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getStatusLabel(request_r4.status), " ");
  }
}
function RequestListComponent_div_51_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Created");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const request_r5 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(2, 1, request_r5.createdAt, "short"), " ");
  }
}
function RequestListComponent_div_51_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 26);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RequestListComponent_div_51_td_16_button_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "button", 32)(1, "mat-icon");
    \u0275\u0275text(2, "edit");
    \u0275\u0275elementEnd()();
  }
}
function RequestListComponent_div_51_td_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 27)(1, "button", 30)(2, "mat-icon");
    \u0275\u0275text(3, "visibility");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(4, RequestListComponent_div_51_td_16_button_4_Template, 3, 0, "button", 31);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const request_r6 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(2, _c0, request_r6.id));
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r2.canEditRequest(request_r6));
  }
}
function RequestListComponent_div_51_tr_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 33);
  }
}
function RequestListComponent_div_51_tr_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 34);
  }
}
function RequestListComponent_div_51_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 35)(1, "mat-icon");
    \u0275\u0275text(2, "assignment");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "No requests found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "You haven't created any requests yet.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "button", 2);
    \u0275\u0275text(8, " Create Your First Request ");
    \u0275\u0275elementEnd()();
  }
}
function RequestListComponent_div_51_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "table", 15);
    \u0275\u0275elementContainerStart(2, 16);
    \u0275\u0275template(3, RequestListComponent_div_51_th_3_Template, 2, 0, "th", 17)(4, RequestListComponent_div_51_td_4_Template, 3, 1, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(5, 19);
    \u0275\u0275template(6, RequestListComponent_div_51_th_6_Template, 2, 0, "th", 17)(7, RequestListComponent_div_51_td_7_Template, 6, 2, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(8, 20);
    \u0275\u0275template(9, RequestListComponent_div_51_th_9_Template, 2, 0, "th", 17)(10, RequestListComponent_div_51_td_10_Template, 3, 3, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(11, 21);
    \u0275\u0275template(12, RequestListComponent_div_51_th_12_Template, 2, 0, "th", 17)(13, RequestListComponent_div_51_td_13_Template, 3, 4, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(14, 22);
    \u0275\u0275template(15, RequestListComponent_div_51_th_15_Template, 2, 0, "th", 17)(16, RequestListComponent_div_51_td_16_Template, 5, 4, "td", 18);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(17, RequestListComponent_div_51_tr_17_Template, 1, 0, "tr", 23)(18, RequestListComponent_div_51_tr_18_Template, 1, 0, "tr", 24);
    \u0275\u0275elementEnd();
    \u0275\u0275template(19, RequestListComponent_div_51_div_19_Template, 9, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("dataSource", ctx_r2.requests);
    \u0275\u0275advance(16);
    \u0275\u0275property("matHeaderRowDef", ctx_r2.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r2.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.requests.length === 0);
  }
}
function RequestListComponent_mat_paginator_52_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-paginator", 36);
    \u0275\u0275listener("page", function RequestListComponent_mat_paginator_52_Template_mat_paginator_page_0_listener($event) {
      \u0275\u0275restoreView(_r7);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPageChange($event));
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275property("length", ctx_r2.totalCount)("pageSize", ctx_r2.pageSize)("pageSizeOptions", \u0275\u0275pureFunction0(4, _c1))("pageIndex", ctx_r2.currentPage - 1);
  }
}
var RequestListComponent = class _RequestListComponent {
  requestService;
  authService;
  destroy$ = new Subject();
  searchSubject = new Subject();
  requests = [];
  displayedColumns = ["id", "title", "status", "createdAt", "actions"];
  loading = false;
  // Pagination
  totalCount = 0;
  currentPage = 1;
  pageSize = 10;
  // Filters
  searchTerm = "";
  selectedStatus = "";
  selectedType = "";
  // Enums for template
  RequestStatus = RequestStatus;
  RequestType = RequestType;
  constructor(requestService, authService) {
    this.requestService = requestService;
    this.authService = authService;
    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {
      this.currentPage = 1;
      this.loadRequests();
    });
  }
  ngOnInit() {
    this.loadRequests();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadRequests() {
    this.loading = true;
    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      searchTerm: this.searchTerm || void 0,
      sortBy: "createdAt",
      sortDirection: "desc"
    };
    this.requestService.getMyRequests(params).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.requests = response.data;
        this.totalCount = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading requests:", error);
        this.loading = false;
      }
    });
  }
  onSearchChange() {
    this.searchSubject.next(this.searchTerm);
  }
  onFilterChange() {
    this.currentPage = 1;
    this.loadRequests();
  }
  onPageChange(event) {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadRequests();
  }
  getRequestTypeLabel(type) {
    switch (type) {
      case RequestType.Leave:
        return "Leave Request";
      case RequestType.Expense:
        return "Expense Report";
      case RequestType.Training:
        return "Training Request";
      case RequestType.ITSupport:
        return "IT Support";
      case RequestType.ProfileUpdate:
        return "Profile Update";
      default:
        return "Unknown";
    }
  }
  getStatusLabel(status) {
    switch (status) {
      case RequestStatus.Pending:
        return "Pending";
      case RequestStatus.Approved:
        return "Approved";
      case RequestStatus.Rejected:
        return "Rejected";
      case RequestStatus.Archived:
        return "Archived";
      default:
        return "Unknown";
    }
  }
  getStatusClass(status) {
    switch (status) {
      case RequestStatus.Pending:
        return "status-pending";
      case RequestStatus.Approved:
        return "status-approved";
      case RequestStatus.Rejected:
        return "status-rejected";
      case RequestStatus.Archived:
        return "status-archived";
      default:
        return "";
    }
  }
  canEditRequest(request) {
    return request.status === RequestStatus.Pending;
  }
  static \u0275fac = function RequestListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RequestListComponent)(\u0275\u0275directiveInject(RequestService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RequestListComponent, selectors: [["app-request-list"]], decls: 53, vars: 15, consts: [[1, "request-list-container"], [1, "header-actions"], ["mat-raised-button", "", "color", "primary", "routerLink", "/requests/new/leave"], [1, "filters"], ["appearance", "outline"], ["matInput", "", "placeholder", "Search requests...", 3, "ngModelChange", "ngModel"], ["matSuffix", ""], [3, "ngModelChange", "selectionChange", "ngModel"], ["value", ""], [3, "value"], ["class", "loading-container", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["showFirstLastButtons", "", 3, "length", "pageSize", "pageSizeOptions", "pageIndex", "page", 4, "ngIf"], [1, "loading-container"], [1, "table-container"], ["mat-table", "", 1, "requests-table", 3, "dataSource"], ["matColumnDef", "id"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "title"], ["matColumnDef", "status"], ["matColumnDef", "createdAt"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["class", "no-data", 4, "ngIf"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "request-id"], [1, "request-title"], ["mat-icon-button", "", "matTooltip", "View Details", 3, "routerLink"], ["mat-icon-button", "", "matTooltip", "Edit", 4, "ngIf"], ["mat-icon-button", "", "matTooltip", "Edit"], ["mat-header-row", ""], ["mat-row", ""], [1, "no-data"], ["showFirstLastButtons", "", 3, "page", "length", "pageSize", "pageSizeOptions", "pageIndex"]], template: function RequestListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title")(4, "mat-icon");
      \u0275\u0275text(5, "assignment");
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " My Requests ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "div", 1)(8, "button", 2)(9, "mat-icon");
      \u0275\u0275text(10, "add");
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " New Request ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "mat-card-content")(13, "div", 3)(14, "mat-form-field", 4)(15, "mat-label");
      \u0275\u0275text(16, "Search");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "input", 5);
      \u0275\u0275twoWayListener("ngModelChange", function RequestListComponent_Template_input_ngModelChange_17_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("ngModelChange", function RequestListComponent_Template_input_ngModelChange_17_listener() {
        return ctx.onSearchChange();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "mat-icon", 6);
      \u0275\u0275text(19, "search");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "mat-form-field", 4)(21, "mat-label");
      \u0275\u0275text(22, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "mat-select", 7);
      \u0275\u0275twoWayListener("ngModelChange", function RequestListComponent_Template_mat_select_ngModelChange_23_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function RequestListComponent_Template_mat_select_selectionChange_23_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(24, "mat-option", 8);
      \u0275\u0275text(25, "All Statuses");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "mat-option", 9);
      \u0275\u0275text(27, "Pending");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "mat-option", 9);
      \u0275\u0275text(29, "Approved");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "mat-option", 9);
      \u0275\u0275text(31, "Rejected");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "mat-option", 9);
      \u0275\u0275text(33, "Archived");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(34, "mat-form-field", 4)(35, "mat-label");
      \u0275\u0275text(36, "Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "mat-select", 7);
      \u0275\u0275twoWayListener("ngModelChange", function RequestListComponent_Template_mat_select_ngModelChange_37_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedType, $event) || (ctx.selectedType = $event);
        return $event;
      });
      \u0275\u0275listener("selectionChange", function RequestListComponent_Template_mat_select_selectionChange_37_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(38, "mat-option", 8);
      \u0275\u0275text(39, "All Types");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "mat-option", 9);
      \u0275\u0275text(41, "Leave");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "mat-option", 9);
      \u0275\u0275text(43, "Expense");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "mat-option", 9);
      \u0275\u0275text(45, "Training");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "mat-option", 9);
      \u0275\u0275text(47, "IT Support");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "mat-option", 9);
      \u0275\u0275text(49, "Profile Update");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(50, RequestListComponent_div_50_Template, 2, 0, "div", 10)(51, RequestListComponent_div_51_Template, 20, 4, "div", 11)(52, RequestListComponent_mat_paginator_52_Template, 1, 5, "mat-paginator", 12);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(17);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedStatus);
      \u0275\u0275advance(3);
      \u0275\u0275property("value", ctx.RequestStatus.Pending);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestStatus.Approved);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestStatus.Rejected);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestStatus.Archived);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedType);
      \u0275\u0275advance(3);
      \u0275\u0275property("value", ctx.RequestType.Leave);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.Expense);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.Training);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.ITSupport);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.RequestType.ProfileUpdate);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.totalCount > 0);
    }
  }, dependencies: [CommonModule, NgIf, DatePipe, RouterModule, RouterLink, MatTableModule, MatTable, MatHeaderCellDef, MatHeaderRowDef, MatColumnDef, MatCellDef, MatRowDef, MatHeaderCell, MatCell, MatHeaderRow, MatRow, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, MatChipsModule, MatChip, MatPaginatorModule, MatPaginator, MatFormFieldModule, MatFormField, MatLabel, MatSuffix, MatInputModule, MatInput, MatSelectModule, MatSelect, MatOption, MatProgressSpinnerModule, MatProgressSpinner, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], styles: ["\n\n.request-list-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  min-width: 200px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.requests-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.request-id[_ngcontent-%COMP%] {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n}\n.request-title[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  display: block;\n}\n.request-title[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.8rem;\n}\n.status-pending[_ngcontent-%COMP%] {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.status-approved[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-rejected[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.status-archived[_ngcontent-%COMP%] {\n  background-color: #e2e3e5;\n  color: #383d41;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #ccc;\n}\n@media (max-width: 768px) {\n  .filters[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .filters[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n    min-width: 100%;\n  }\n}\n/*# sourceMappingURL=request-list.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RequestListComponent, [{
    type: Component,
    args: [{ selector: "app-request-list", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatTableModule,
      MatButtonModule,
      MatIconModule,
      MatCardModule,
      MatChipsModule,
      MatPaginatorModule,
      MatFormFieldModule,
      MatInputModule,
      MatSelectModule,
      MatProgressSpinnerModule,
      FormsModule
    ], template: `
    <div class="request-list-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>assignment</mat-icon>
            My Requests
          </mat-card-title>
          <div class="header-actions">
            <button mat-raised-button color="primary" routerLink="/requests/new/leave">
              <mat-icon>add</mat-icon>
              New Request
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <!-- Filters -->
          <div class="filters">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()" placeholder="Search requests...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [(ngModel)]="selectedStatus" (selectionChange)="onFilterChange()">
                <mat-option value="">All Statuses</mat-option>
                <mat-option [value]="RequestStatus.Pending">Pending</mat-option>
                <mat-option [value]="RequestStatus.Approved">Approved</mat-option>
                <mat-option [value]="RequestStatus.Rejected">Rejected</mat-option>
                <mat-option [value]="RequestStatus.Archived">Archived</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Type</mat-label>
              <mat-select [(ngModel)]="selectedType" (selectionChange)="onFilterChange()">
                <mat-option value="">All Types</mat-option>
                <mat-option [value]="RequestType.Leave">Leave</mat-option>
                <mat-option [value]="RequestType.Expense">Expense</mat-option>
                <mat-option [value]="RequestType.Training">Training</mat-option>
                <mat-option [value]="RequestType.ITSupport">IT Support</mat-option>
                <mat-option [value]="RequestType.ProfileUpdate">Profile Update</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <mat-spinner></mat-spinner>
          </div>

          <!-- Requests Table -->
          <div *ngIf="!loading" class="table-container">
            <table mat-table [dataSource]="requests" class="requests-table">
              <!-- ID Column -->
              <ng-container matColumnDef="id">
                <th mat-header-cell *matHeaderCellDef>ID</th>
                <td mat-cell *matCellDef="let request">
                  <span class="request-id">{{request.id.substring(0, 8)}}</span>
                </td>
              </ng-container>

              <!-- Title Column -->
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Title</th>
                <td mat-cell *matCellDef="let request">
                  <div class="request-title">
                    <strong>{{request.title || 'No Title'}}</strong>
                    <small>{{getRequestTypeLabel(request.type)}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let request">
                  <mat-chip [class]="getStatusClass(request.status)">
                    {{getStatusLabel(request.status)}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Created Date Column -->
              <ng-container matColumnDef="createdAt">
                <th mat-header-cell *matHeaderCellDef>Created</th>
                <td mat-cell *matCellDef="let request">
                  {{request.createdAt | date:'short'}}
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let request">
                  <button mat-icon-button [routerLink]="['/requests/details', request.id]" matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button *ngIf="canEditRequest(request)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- No Data Message -->
            <div *ngIf="requests.length === 0" class="no-data">
              <mat-icon>assignment</mat-icon>
              <h3>No requests found</h3>
              <p>You haven't created any requests yet.</p>
              <button mat-raised-button color="primary" routerLink="/requests/new/leave">
                Create Your First Request
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator
            *ngIf="!loading && totalCount > 0"
            [length]="totalCount"
            [pageSize]="pageSize"
            [pageSizeOptions]="[5, 10, 25, 50]"
            [pageIndex]="currentPage - 1"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;a379b2626ec1be5dc7408e830fba503ae306c9c6547b1a2160430b970d196e56;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/requests/components/request-list/request-list.component.ts */\n.request-list-container {\n  padding: 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\nmat-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\nmat-card-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  flex-wrap: wrap;\n}\n.filters mat-form-field {\n  min-width: 200px;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.table-container {\n  overflow-x: auto;\n}\n.requests-table {\n  width: 100%;\n}\n.request-id {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n}\n.request-title strong {\n  display: block;\n}\n.request-title small {\n  color: #666;\n  font-size: 0.8rem;\n}\n.status-pending {\n  background-color: #fff3cd;\n  color: #856404;\n}\n.status-approved {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-rejected {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.status-archived {\n  background-color: #e2e3e5;\n  color: #383d41;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #666;\n}\n.no-data mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  margin-bottom: 1rem;\n  color: #ccc;\n}\n@media (max-width: 768px) {\n  .filters {\n    flex-direction: column;\n  }\n  .filters mat-form-field {\n    min-width: 100%;\n  }\n}\n/*# sourceMappingURL=request-list.component.css.map */\n"] }]
  }], () => [{ type: RequestService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RequestListComponent, { className: "RequestListComponent", filePath: "src/app/features/requests/components/request-list/request-list.component.ts", lineNumber: 282 });
})();
export {
  RequestListComponent
};
//# sourceMappingURL=chunk-FHFXZCVY.js.map
