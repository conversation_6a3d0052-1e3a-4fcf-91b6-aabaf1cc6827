{"version": 3, "sources": ["src/app/features/workflows/components/workflow-designer/workflow-designer.component.ts"], "sourcesContent": ["\n    .workflow-designer-container {\n      padding: 1rem;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 3rem;\n    }\n\n    .workflow-info-card,\n    .workflow-steps-card {\n      margin-bottom: 1rem;\n    }\n\n    mat-card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    mat-card-title {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 1rem;\n      align-items: flex-start;\n    }\n\n    .full-width {\n      width: 100%;\n    }\n\n    .step-item {\n      margin-bottom: 1.5rem;\n      padding: 1rem;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      background-color: #fafafa;\n    }\n\n    .step-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .step-header h4 {\n      margin: 0;\n      color: #333;\n    }\n\n    .step-form {\n      background-color: white;\n      padding: 1rem;\n      border-radius: 4px;\n    }\n\n    .no-steps {\n      text-align: center;\n      padding: 2rem;\n      color: #666;\n    }\n\n    .no-steps mat-icon {\n      font-size: 2rem;\n      width: 2rem;\n      height: 2rem;\n      margin-bottom: 0.5rem;\n      color: #ff9800;\n    }\n\n    .actions-section {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      padding: 1rem;\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      margin-top: 1rem;\n    }\n\n    @media (max-width: 768px) {\n      .form-row {\n        flex-direction: column;\n      }\n\n      .actions-section {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AAAA,CAAA;AAEE,iBAAA;;AAGF;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,oBAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAPA,YAOA;AACE,UAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA;;AAGF,CANA,SAMA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;AACA,cAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA9DF;AA+DI,oBAAA;;AAGF,GAfF;AAgBI,oBAAA;;;", "names": []}