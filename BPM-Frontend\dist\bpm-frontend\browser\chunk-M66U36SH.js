import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MatStepperModule
} from "./chunk-3CZ3YMFZ.js";
import {
  WorkflowService
} from "./chunk-ILRPBHOG.js";
import "./chunk-H3UX3NVF.js";
import {
  MatTooltipModule
} from "./chunk-N7MY3IFD.js";
import {
  MatDiv<PERSON>,
  MatDividerModule
} from "./chunk-7XGPZDLL.js";
import "./chunk-W6VU2H2S.js";
import {
  MatChip,
  MatChipsModule
} from "./chunk-DALMYUGW.js";
import "./chunk-R3ISBMK2.js";
import "./chunk-D2SQUTFC.js";
import {
  MatProgressSpinner,
  MatProgressSpinnerModule
} from "./chunk-L52YVPND.js";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  <PERSON><PERSON><PERSON>er<PERSON>ell,
  MatHeaderCellDef,
  <PERSON><PERSON><PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>erRowDef,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  MatTableModule
} from "./chunk-PJTOMNHB.js";
import "./chunk-GXLB3RCJ.js";
import "./chunk-FT7WN52K.js";
import "./chunk-YJIBYHOF.js";
import {
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle
} from "./chunk-CARSBOV6.js";
import "./chunk-AKJJBQK4.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-EG2VWXTC.js";
import {
  MatButton,
  MatButtonModule
} from "./chunk-5XTAYFTV.js";
import {
  ActivatedRoute,
  RouterLink,
  RouterModule
} from "./chunk-ESNMJU6B.js";
import {
  CommonModule,
  DatePipe,
  NgForOf,
  NgIf
} from "./chunk-RCHKY2RO.js";
import {
  Component,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-JTRMQXMJ.js";

// src/app/features/workflows/components/workflow-details/workflow-details.component.ts
var _c0 = (a0) => ["/workflows/designer", a0];
function WorkflowDetailsComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4);
    \u0275\u0275element(1, "mat-spinner");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDetailsComponent_div_2_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "strong");
    \u0275\u0275text(2, "Last Updated:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275pipe(5, "date");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(5, 1, ctx_r0.workflow.updatedAt, "full"));
  }
}
function WorkflowDetailsComponent_div_2_div_35_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18)(1, "h3");
    \u0275\u0275text(2, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.workflow.description);
  }
}
function WorkflowDetailsComponent_div_2_div_52_mat_step_2_ng_template_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 37)(1, "span", 38);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 39);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(step_r2.stepName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Step ", step_r2.order, "");
  }
}
function WorkflowDetailsComponent_div_2_div_52_mat_step_2_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34)(1, "strong");
    \u0275\u0275text(2, "Due Time:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("", step_r2.dueInHours, " hours");
  }
}
function WorkflowDetailsComponent_div_2_div_52_mat_step_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-step");
    \u0275\u0275template(1, WorkflowDetailsComponent_div_2_div_52_mat_step_2_ng_template_1_Template, 5, 2, "ng-template", 31);
    \u0275\u0275elementStart(2, "div", 32)(3, "div", 33)(4, "div", 34)(5, "strong");
    \u0275\u0275text(6, "Responsible Role:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "mat-chip", 35);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(9, WorkflowDetailsComponent_div_2_div_52_mat_step_2_div_9_Template, 5, 1, "div", 36);
    \u0275\u0275elementStart(10, "div", 34)(11, "strong");
    \u0275\u0275text(12, "Created:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span");
    \u0275\u0275text(14);
    \u0275\u0275pipe(15, "date");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const step_r2 = ctx.$implicit;
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(step_r2.responsibleRole);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", step_r2.dueInHours);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(15, 3, step_r2.createdAt, "short"));
  }
}
function WorkflowDetailsComponent_div_2_div_52_th_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 40);
    \u0275\u0275text(1, "Order");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDetailsComponent_div_2_div_52_td_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 41);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(step_r3.order);
  }
}
function WorkflowDetailsComponent_div_2_div_52_th_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 40);
    \u0275\u0275text(1, "Step Name");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDetailsComponent_div_2_div_52_td_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 41);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r4 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(step_r4.stepName);
  }
}
function WorkflowDetailsComponent_div_2_div_52_th_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 40);
    \u0275\u0275text(1, "Responsible Role");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDetailsComponent_div_2_div_52_td_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 41)(1, "mat-chip", 35);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const step_r5 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(step_r5.responsibleRole);
  }
}
function WorkflowDetailsComponent_div_2_div_52_th_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 40);
    \u0275\u0275text(1, "Due Time");
    \u0275\u0275elementEnd();
  }
}
function WorkflowDetailsComponent_div_2_div_52_td_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 41);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const step_r6 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", step_r6.dueInHours ? step_r6.dueInHours + " hours" : "No limit", " ");
  }
}
function WorkflowDetailsComponent_div_2_div_52_tr_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 42);
  }
}
function WorkflowDetailsComponent_div_2_div_52_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 43);
  }
}
function WorkflowDetailsComponent_div_2_div_52_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "mat-stepper", 19);
    \u0275\u0275template(2, WorkflowDetailsComponent_div_2_div_52_mat_step_2_Template, 16, 6, "mat-step", 20);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 21)(4, "h4");
    \u0275\u0275text(5, "Steps Summary");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "table", 22);
    \u0275\u0275elementContainerStart(7, 23);
    \u0275\u0275template(8, WorkflowDetailsComponent_div_2_div_52_th_8_Template, 2, 0, "th", 24)(9, WorkflowDetailsComponent_div_2_div_52_td_9_Template, 2, 1, "td", 25);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(10, 26);
    \u0275\u0275template(11, WorkflowDetailsComponent_div_2_div_52_th_11_Template, 2, 0, "th", 24)(12, WorkflowDetailsComponent_div_2_div_52_td_12_Template, 2, 1, "td", 25);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(13, 27);
    \u0275\u0275template(14, WorkflowDetailsComponent_div_2_div_52_th_14_Template, 2, 0, "th", 24)(15, WorkflowDetailsComponent_div_2_div_52_td_15_Template, 3, 1, "td", 25);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(16, 28);
    \u0275\u0275template(17, WorkflowDetailsComponent_div_2_div_52_th_17_Template, 2, 0, "th", 24)(18, WorkflowDetailsComponent_div_2_div_52_td_18_Template, 2, 1, "td", 25);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(19, WorkflowDetailsComponent_div_2_div_52_tr_19_Template, 1, 0, "tr", 29)(20, WorkflowDetailsComponent_div_2_div_52_tr_20_Template, 1, 0, "tr", 30);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("linear", false);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.sortedSteps);
    \u0275\u0275advance(4);
    \u0275\u0275property("dataSource", ctx_r0.sortedSteps);
    \u0275\u0275advance(13);
    \u0275\u0275property("matHeaderRowDef", ctx_r0.stepColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r0.stepColumns);
  }
}
function WorkflowDetailsComponent_div_2_div_53_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 44)(1, "mat-icon");
    \u0275\u0275text(2, "info");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h4");
    \u0275\u0275text(4, "No Steps Defined");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "This workflow doesn't have any steps configured yet.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "button", 15);
    \u0275\u0275text(8, " Add Steps ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(7);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(1, _c0, ctx_r0.workflow.id));
  }
}
function WorkflowDetailsComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "mat-card", 5)(2, "mat-card-header")(3, "mat-card-title")(4, "div", 6)(5, "mat-icon");
    \u0275\u0275text(6, "account_tree");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div")(8, "h2");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "span", 7);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(12, "div", 8)(13, "mat-chip");
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(15, "mat-card-content")(16, "div", 9)(17, "div", 10)(18, "strong");
    \u0275\u0275text(19, "Workflow ID:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "span", 11);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 10)(23, "strong");
    \u0275\u0275text(24, "Created:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "span");
    \u0275\u0275text(26);
    \u0275\u0275pipe(27, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(28, WorkflowDetailsComponent_div_2_div_28_Template, 6, 4, "div", 12);
    \u0275\u0275elementStart(29, "div", 10)(30, "strong");
    \u0275\u0275text(31, "Total Steps:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(32, "span");
    \u0275\u0275text(33);
    \u0275\u0275elementEnd()()();
    \u0275\u0275element(34, "mat-divider");
    \u0275\u0275template(35, WorkflowDetailsComponent_div_2_div_35_Template, 5, 1, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "mat-card-actions")(37, "button", 14)(38, "mat-icon");
    \u0275\u0275text(39, "arrow_back");
    \u0275\u0275elementEnd();
    \u0275\u0275text(40, " Back to Workflows ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(41, "button", 15)(42, "mat-icon");
    \u0275\u0275text(43, "edit");
    \u0275\u0275elementEnd();
    \u0275\u0275text(44, " Edit Workflow ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(45, "mat-card", 16)(46, "mat-card-header")(47, "mat-card-title")(48, "mat-icon");
    \u0275\u0275text(49, "timeline");
    \u0275\u0275elementEnd();
    \u0275\u0275text(50, " Workflow Steps ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(51, "mat-card-content");
    \u0275\u0275template(52, WorkflowDetailsComponent_div_2_div_52_Template, 21, 5, "div", 2)(53, WorkflowDetailsComponent_div_2_div_53_Template, 9, 3, "div", 17);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r0.workflow.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Version ", ctx_r0.workflow.version, "");
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r0.workflow.isActive ? "status-active" : "status-inactive");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.workflow.isActive ? "Active" : "Inactive", " ");
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.workflow.id);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(27, 13, ctx_r0.workflow.createdAt, "full"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.workflow.updatedAt);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.workflow.steps ? ctx_r0.workflow.steps.length : 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.workflow.description);
    \u0275\u0275advance(6);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(16, _c0, ctx_r0.workflow.id));
    \u0275\u0275advance(11);
    \u0275\u0275property("ngIf", ctx_r0.workflow.steps && ctx_r0.workflow.steps.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.workflow.steps || ctx_r0.workflow.steps.length === 0);
  }
}
function WorkflowDetailsComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 45)(1, "mat-card")(2, "mat-card-content")(3, "div", 46)(4, "mat-icon");
    \u0275\u0275text(5, "error");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "Workflow Not Found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "The workflow you're looking for doesn't exist or has been removed.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "button", 47);
    \u0275\u0275text(11, " Back to Workflows ");
    \u0275\u0275elementEnd()()()()();
  }
}
var WorkflowDetailsComponent = class _WorkflowDetailsComponent {
  route;
  workflowService;
  destroy$ = new Subject();
  workflow = null;
  loading = false;
  workflowId;
  stepColumns = ["order", "stepName", "responsibleRole", "dueInHours"];
  constructor(route, workflowService) {
    this.route = route;
    this.workflowService = workflowService;
    this.workflowId = this.route.snapshot.params["id"];
  }
  ngOnInit() {
    this.loadWorkflowDetails();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  get sortedSteps() {
    if (!this.workflow?.steps)
      return [];
    return [...this.workflow.steps].sort((a, b) => a.order - b.order);
  }
  loadWorkflowDetails() {
    this.loading = true;
    this.workflowService.getWorkflowWithSteps(this.workflowId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (workflow) => {
        this.workflow = workflow;
        this.loading = false;
      },
      error: (error) => {
        console.error("Error loading workflow details:", error);
        this.workflow = null;
        this.loading = false;
      }
    });
  }
  static \u0275fac = function WorkflowDetailsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowDetailsComponent)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(WorkflowService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowDetailsComponent, selectors: [["app-workflow-details"]], decls: 4, vars: 3, consts: [[1, "workflow-details-container"], ["class", "loading-container", 4, "ngIf"], [4, "ngIf"], ["class", "error-container", 4, "ngIf"], [1, "loading-container"], [1, "header-card"], [1, "title-section"], [1, "workflow-version"], [1, "status-section"], [1, "workflow-info"], [1, "info-item"], [1, "workflow-id"], ["class", "info-item", 4, "ngIf"], ["class", "description-section", 4, "ngIf"], ["mat-button", "", "routerLink", "/workflows"], ["mat-raised-button", "", "color", "primary", 3, "routerLink"], [1, "steps-card"], ["class", "no-steps", 4, "ngIf"], [1, "description-section"], ["orientation", "vertical", 1, "workflow-stepper", 3, "linear"], [4, "ngFor", "ngForOf"], [1, "steps-table-section"], ["mat-table", "", 1, "steps-table", 3, "dataSource"], ["matColumnDef", "order"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "stepName"], ["matColumnDef", "responsibleRole"], ["matColumnDef", "dueInHours"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["matStepLabel", ""], [1, "step-content"], [1, "step-details"], [1, "step-detail"], [1, "role-chip"], ["class", "step-detail", 4, "ngIf"], [1, "step-label"], [1, "step-name"], [1, "step-order"], ["mat-header-cell", ""], ["mat-cell", ""], ["mat-header-row", ""], ["mat-row", ""], [1, "no-steps"], [1, "error-container"], [1, "error-content"], ["mat-raised-button", "", "color", "primary", "routerLink", "/workflows"]], template: function WorkflowDetailsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, WorkflowDetailsComponent_div_1_Template, 2, 0, "div", 1)(2, WorkflowDetailsComponent_div_2_Template, 54, 18, "div", 2)(3, WorkflowDetailsComponent_div_3_Template, 12, 0, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.workflow);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && !ctx.workflow);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    DatePipe,
    RouterModule,
    RouterLink,
    MatCardModule,
    MatCard,
    MatCardActions,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatChipsModule,
    MatChip,
    MatDividerModule,
    MatDivider,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatStepperModule,
    MatStep,
    MatStepLabel,
    MatStepper,
    MatTooltipModule,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow
  ], styles: ["\n\n.workflow-details-container[_ngcontent-%COMP%] {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.header-card[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.title-section[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.5rem;\n}\n.workflow-version[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  font-weight: normal;\n}\nmat-card-title[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  width: 100%;\n}\n.workflow-info[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin: 1rem 0;\n}\n.info-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.workflow-id[_ngcontent-%COMP%] {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n.description-section[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.description-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n.description-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  line-height: 1.6;\n  color: #666;\n}\n.steps-card[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n}\n.workflow-stepper[_ngcontent-%COMP%] {\n  margin: 1rem 0;\n}\n.step-label[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.step-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.step-order[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #666;\n}\n.step-content[_ngcontent-%COMP%] {\n  padding: 1rem 0;\n}\n.step-details[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n}\n.step-detail[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.role-chip[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.steps-table-section[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n}\n.steps-table-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n  color: #333;\n}\n.steps-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.status-active[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-inactive[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.no-steps[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.no-steps[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  color: #ff9800;\n  margin-bottom: 1rem;\n}\n.error-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.error-content[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n}\n.error-content[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  color: #f44336;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  mat-card-title[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .workflow-info[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=workflow-details.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowDetailsComponent, [{
    type: Component,
    args: [{ selector: "app-workflow-details", standalone: true, imports: [
      CommonModule,
      RouterModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatChipsModule,
      MatDividerModule,
      MatProgressSpinnerModule,
      MatStepperModule,
      MatTooltipModule,
      MatTableModule
    ], template: `
    <div class="workflow-details-container">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <!-- Workflow Details -->
      <div *ngIf="!loading && workflow">
        <!-- Header Card -->
        <mat-card class="header-card">
          <mat-card-header>
            <mat-card-title>
              <div class="title-section">
                <mat-icon>account_tree</mat-icon>
                <div>
                  <h2>{{workflow.name}}</h2>
                  <span class="workflow-version">Version {{workflow.version}}</span>
                </div>
              </div>
              <div class="status-section">
                <mat-chip [class]="workflow.isActive ? 'status-active' : 'status-inactive'">
                  {{workflow.isActive ? 'Active' : 'Inactive'}}
                </mat-chip>
              </div>
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <div class="workflow-info">
              <div class="info-item">
                <strong>Workflow ID:</strong>
                <span class="workflow-id">{{workflow.id}}</span>
              </div>
              <div class="info-item">
                <strong>Created:</strong>
                <span>{{workflow.createdAt | date:'full'}}</span>
              </div>
              <div class="info-item" *ngIf="workflow.updatedAt">
                <strong>Last Updated:</strong>
                <span>{{workflow.updatedAt | date:'full'}}</span>
              </div>
              <div class="info-item">
                <strong>Total Steps:</strong>
                <span>{{workflow.steps ? workflow.steps.length : 0}}</span>
              </div>
            </div>

            <mat-divider></mat-divider>

            <div class="description-section" *ngIf="workflow.description">
              <h3>Description</h3>
              <p>{{workflow.description}}</p>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button routerLink="/workflows">
              <mat-icon>arrow_back</mat-icon>
              Back to Workflows
            </button>
            <button mat-raised-button color="primary" [routerLink]="['/workflows/designer', workflow.id]">
              <mat-icon>edit</mat-icon>
              Edit Workflow
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Workflow Steps -->
        <mat-card class="steps-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>timeline</mat-icon>
              Workflow Steps
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <div *ngIf="workflow.steps && workflow.steps.length > 0">
              <!-- Visual Stepper -->
              <mat-stepper [linear]="false" orientation="vertical" class="workflow-stepper">
                <mat-step *ngFor="let step of sortedSteps; let i = index">
                  <ng-template matStepLabel>
                    <div class="step-label">
                      <span class="step-name">{{step.stepName}}</span>
                      <span class="step-order">Step {{step.order}}</span>
                    </div>
                  </ng-template>

                  <div class="step-content">
                    <div class="step-details">
                      <div class="step-detail">
                        <strong>Responsible Role:</strong>
                        <mat-chip class="role-chip">{{step.responsibleRole}}</mat-chip>
                      </div>
                      <div class="step-detail" *ngIf="step.dueInHours">
                        <strong>Due Time:</strong>
                        <span>{{step.dueInHours}} hours</span>
                      </div>
                      <div class="step-detail">
                        <strong>Created:</strong>
                        <span>{{step.createdAt | date:'short'}}</span>
                      </div>
                    </div>
                  </div>
                </mat-step>
              </mat-stepper>

              <!-- Steps Table -->
              <div class="steps-table-section">
                <h4>Steps Summary</h4>
                <table mat-table [dataSource]="sortedSteps" class="steps-table">
                  <!-- Order Column -->
                  <ng-container matColumnDef="order">
                    <th mat-header-cell *matHeaderCellDef>Order</th>
                    <td mat-cell *matCellDef="let step">{{step.order}}</td>
                  </ng-container>

                  <!-- Step Name Column -->
                  <ng-container matColumnDef="stepName">
                    <th mat-header-cell *matHeaderCellDef>Step Name</th>
                    <td mat-cell *matCellDef="let step">{{step.stepName}}</td>
                  </ng-container>

                  <!-- Responsible Role Column -->
                  <ng-container matColumnDef="responsibleRole">
                    <th mat-header-cell *matHeaderCellDef>Responsible Role</th>
                    <td mat-cell *matCellDef="let step">
                      <mat-chip class="role-chip">{{step.responsibleRole}}</mat-chip>
                    </td>
                  </ng-container>

                  <!-- Due Time Column -->
                  <ng-container matColumnDef="dueInHours">
                    <th mat-header-cell *matHeaderCellDef>Due Time</th>
                    <td mat-cell *matCellDef="let step">
                      {{step.dueInHours ? step.dueInHours + ' hours' : 'No limit'}}
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="stepColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: stepColumns;"></tr>
                </table>
              </div>
            </div>

            <div *ngIf="!workflow.steps || workflow.steps.length === 0" class="no-steps">
              <mat-icon>info</mat-icon>
              <h4>No Steps Defined</h4>
              <p>This workflow doesn't have any steps configured yet.</p>
              <button mat-raised-button color="primary" [routerLink]="['/workflows/designer', workflow.id]">
                Add Steps
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Error State -->
      <div *ngIf="!loading && !workflow" class="error-container">
        <mat-card>
          <mat-card-content>
            <div class="error-content">
              <mat-icon>error</mat-icon>
              <h3>Workflow Not Found</h3>
              <p>The workflow you're looking for doesn't exist or has been removed.</p>
              <button mat-raised-button color="primary" routerLink="/workflows">
                Back to Workflows
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;6614d6624109ebf37c2e1754413f23b368a83ce319c1047bd6cc57bf0adf5483;C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/src/app/features/workflows/components/workflow-details/workflow-details.component.ts */\n.workflow-details-container {\n  padding: 1rem;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 3rem;\n}\n.header-card {\n  margin-bottom: 1rem;\n}\n.title-section {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-section h2 {\n  margin: 0;\n  font-size: 1.5rem;\n}\n.workflow-version {\n  color: #666;\n  font-size: 0.9rem;\n  font-weight: normal;\n}\nmat-card-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  width: 100%;\n}\n.workflow-info {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin: 1rem 0;\n}\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.workflow-id {\n  font-family: monospace;\n  background-color: #f5f5f5;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n.description-section {\n  margin-top: 1rem;\n}\n.description-section h3 {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n.description-section p {\n  line-height: 1.6;\n  color: #666;\n}\n.steps-card {\n  margin-top: 1rem;\n}\n.workflow-stepper {\n  margin: 1rem 0;\n}\n.step-label {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.step-name {\n  font-weight: 500;\n}\n.step-order {\n  font-size: 0.8rem;\n  color: #666;\n}\n.step-content {\n  padding: 1rem 0;\n}\n.step-details {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n}\n.step-detail {\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.role-chip {\n  background-color: #e3f2fd;\n  color: #1976d2;\n  font-size: 0.75rem;\n}\n.steps-table-section {\n  margin-top: 2rem;\n}\n.steps-table-section h4 {\n  margin-bottom: 1rem;\n  color: #333;\n}\n.steps-table {\n  width: 100%;\n}\n.status-active {\n  background-color: #d4edda;\n  color: #155724;\n}\n.status-inactive {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n.no-steps {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n.no-steps mat-icon {\n  font-size: 3rem;\n  width: 3rem;\n  height: 3rem;\n  color: #ff9800;\n  margin-bottom: 1rem;\n}\n.error-container {\n  display: flex;\n  justify-content: center;\n  padding: 2rem;\n}\n.error-content {\n  text-align: center;\n  padding: 2rem;\n}\n.error-content mat-icon {\n  font-size: 4rem;\n  width: 4rem;\n  height: 4rem;\n  color: #f44336;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  mat-card-title {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .workflow-info {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=workflow-details.component.css.map */\n"] }]
  }], () => [{ type: ActivatedRoute }, { type: WorkflowService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowDetailsComponent, { className: "WorkflowDetailsComponent", filePath: "src/app/features/workflows/components/workflow-details/workflow-details.component.ts", lineNumber: 400 });
})();
export {
  WorkflowDetailsComponent
};
//# sourceMappingURL=chunk-M66U36SH.js.map
