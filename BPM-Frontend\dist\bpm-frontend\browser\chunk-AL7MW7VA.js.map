{"version": 3, "sources": ["src/app/features/admin/components/admin-not-found/admin-not-found.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCardModule } from '@angular/material/card';\n\n@Component({\n  selector: 'app-admin-not-found',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatButtonModule,\n    MatIconModule,\n    MatCardModule\n  ],\n  template: `\n    <div class=\"not-found-container\">\n      <mat-card class=\"not-found-card\">\n        <mat-card-content>\n          <div class=\"error-icon\">\n            <mat-icon>admin_panel_settings</mat-icon>\n          </div>\n          <h1>Admin Page Not Found</h1>\n          <p>The administrative page you're looking for doesn't exist or has been moved.</p>\n          <div class=\"suggestions\">\n            <h3>Available Admin Functions:</h3>\n            <ul>\n              <li>User Management - Manage system users and permissions</li>\n              <li>Role Management - Configure user roles and access levels</li>\n              <li>Workflow Designer - Create and modify business workflows</li>\n              <li>System Settings - Configure application settings</li>\n              <li>Reports - View system analytics and reports</li>\n            </ul>\n          </div>\n          <div class=\"actions\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/admin/users\">\n              <mat-icon>people</mat-icon>\n              User Management\n            </button>\n            <button mat-raised-button color=\"accent\" routerLink=\"/admin/workflow-designer\">\n              <mat-icon>account_tree</mat-icon>\n              Workflow Designer\n            </button>\n            <button mat-button routerLink=\"/dashboard\">\n              <mat-icon>home</mat-icon>\n              Dashboard\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .not-found-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 2rem;\n    }\n\n    .not-found-card {\n      max-width: 600px;\n      text-align: center;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      margin-bottom: 1rem;\n    }\n\n    .error-icon mat-icon {\n      font-size: 64px;\n      width: 64px;\n      height: 64px;\n      color: #ff9800;\n    }\n\n    h1 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 2rem;\n    }\n\n    p {\n      color: #666;\n      margin-bottom: 1.5rem;\n      line-height: 1.6;\n      font-size: 1.1rem;\n    }\n\n    .suggestions {\n      text-align: left;\n      margin: 2rem 0;\n      padding: 1rem;\n      background-color: #f8f9fa;\n      border-radius: 8px;\n    }\n\n    .suggestions h3 {\n      color: #333;\n      margin-bottom: 1rem;\n      font-size: 1.2rem;\n    }\n\n    .suggestions ul {\n      margin: 0;\n      padding-left: 1.5rem;\n    }\n\n    .suggestions li {\n      color: #666;\n      margin-bottom: 0.5rem;\n      line-height: 1.4;\n    }\n\n    .actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n      flex-wrap: wrap;\n      margin-top: 2rem;\n    }\n\n    @media (max-width: 480px) {\n      .actions {\n        flex-direction: column;\n      }\n      \n      .not-found-card {\n        padding: 1rem;\n      }\n      \n      h1 {\n        font-size: 1.5rem;\n      }\n    }\n  `]\n})\nexport class AdminNotFoundComponent {\n  constructor() {}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IM,IAAO,yBAAP,MAAO,wBAAsB;EACjC,cAAA;EAAe;;qCADJ,yBAAsB;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,cAAA,cAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,UAAA,cAAA,0BAAA,GAAA,CAAA,cAAA,IAAA,cAAA,YAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA5H/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,YAAA,CAAA,EACE,GAAA,kBAAA,EACb,GAAA,OAAA,CAAA,EACQ,GAAA,UAAA;AACZ,MAAA,iBAAA,GAAA,sBAAA;AAAoB,MAAA,uBAAA,EAAW;AAE3C,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,sBAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,6EAAA;AAA2E,MAAA,uBAAA;AAC9E,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAyB,IAAA,IAAA;AACnB,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,MAAA,iBAAA,IAAA,uDAAA;AAAqD,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,0DAAA;AAAwD,MAAA,uBAAA;AAC5D,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,0DAAA;AAAwD,MAAA,uBAAA;AAC5D,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,kDAAA;AAAgD,MAAA,uBAAA;AACpD,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,6CAAA;AAA2C,MAAA,uBAAA,EAAK,EACjD;AAEP,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAqB,IAAA,UAAA,CAAA,EACiD,IAAA,UAAA;AACxD,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChB,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA+E,IAAA,UAAA;AACnE,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACtB,MAAA,iBAAA,IAAA,qBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA2C,IAAA,UAAA;AAC/B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACd,MAAA,iBAAA,IAAA,aAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACW,EACV;;;IAxCb;IACA;IAAY;IACZ;IAAe;IACf;IAAa;IACb;IAAa;IAAA;EAAA,GAAA,QAAA,CAAA,qoDAAA,EAAA,CAAA;;;sEA+HJ,wBAAsB,CAAA;UAvIlC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoCT,QAAA,CAAA,ygDAAA,EAAA,CAAA;;;;6EAyFU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}